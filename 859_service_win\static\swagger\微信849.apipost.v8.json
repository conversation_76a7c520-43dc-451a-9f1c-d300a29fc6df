{"project_id": "451b2e53cd29b", "name": "微信849", "intro": "wechat-ipad849 WEB-API 支持长链接、自动心跳", "global": {"envs": [{"env_id": "1", "name": "默认环境", "is_private": 1, "sort": 1, "server_list": [{"server_id": "1", "name": "默认服务", "sort": 0, "uri": "http://127.0.0.1:8848"}, {"server_id": "451b2e53cd29c", "name": "远程服务", "sort": 0, "uri": "http://*************:8848"}, {"server_id": "451b2e53cd29d", "name": "本地服务", "sort": 0, "uri": "http://127.0.0.1:8848"}, {"server_id": "451b2e53cd29e", "name": "TMP服务", "sort": 0, "uri": ""}], "env_var_list": {"ADMIN_KEY": {"value": "", "current_value": "", "description": "【管理接口的Key】"}, "TOKEN_KEY": {"value": "", "current_value": "", "description": "【WX接口的Key】"}, "SOCKS5": {"value": "socks5://username:password@ipv4:port", "current_value": "socks5://username:password@ipv4:port", "description": "【socks5代理】"}, "Wxid": {"value": "", "current_value": "", "description": "【当前登录账号wxid】"}, "Wxid1": {"value": "", "current_value": "", "description": "【接收消息的wxid】"}, "A16_DATA": {"value": "", "current_value": "", "description": "【A16数据】"}, "A62_DATA": {"value": "", "current_value": "", "description": "【A62数据】"}, "QID": {"value": "", "current_value": "", "description": "【测试群组ID】"}}}, {"env_id": "2", "name": "Mo<PERSON>环境", "is_private": 1, "sort": 2, "server_list": [{"server_id": "1", "name": "默认服务", "sort": 1000, "uri": "https://mock.apipost.net/mock/451b2e53cd29b"}, {"server_id": "451b2e53cd29c", "name": "远程服务", "sort": 0, "uri": ""}, {"server_id": "451b2e53cd29d", "name": "本地服务", "sort": 0, "uri": ""}, {"server_id": "451b2e53cd29e", "name": "TMP服务", "sort": 0, "uri": ""}], "env_var_list": {}}, {"env_id": "451b2e57cd2cc", "name": "明月清风的环境", "is_private": 1, "sort": 0, "server_list": [{"server_id": "1", "name": "默认服务", "sort": 0, "uri": "http://127.0.0.1:8848"}, {"server_id": "451b2e53cd29c", "name": "远程服务", "sort": 0, "uri": "http://*************:8848"}, {"server_id": "451b2e53cd29d", "name": "本地服务", "sort": 0, "uri": "http://127.0.0.1:8848"}, {"server_id": "451b2e53cd29e", "name": "TMP服务", "sort": 0, "uri": ""}], "env_var_list": {"ADMIN_KEY": {"value": "", "current_value": "", "description": "【管理接口的Key】"}, "TOKEN_KEY": {"value": "", "current_value": "", "description": "【WX接口的Key】"}, "SOCKS5": {"value": "socks5://username:password@ipv4:port", "current_value": "socks5://username:password@ipv4:port", "description": "【socks5代理】"}, "Wxid": {"value": "", "current_value": "", "description": "【当前登录账号wxid】"}, "Wxid1": {"value": "", "current_value": "", "description": "【接收消息的wxid】"}, "A16_DATA": {"value": "", "current_value": "", "description": "【A16数据】"}, "A62_DATA": {"value": "", "current_value": "", "description": "【A62数据】"}, "QID": {"value": "", "current_value": "", "description": "【测试群组ID】"}}}], "servers": [{"server_id": "1", "name": "默认服务", "sort": 1000}, {"server_id": "451b2e53cd29c", "name": "远程服务", "sort": 2001}, {"server_id": "451b2e53cd29d", "name": "本地服务", "sort": 2002}, {"server_id": "451b2e53cd29e", "name": "TMP服务", "sort": 2003}], "global_vars": {}, "global_param": {"header": {"parameter": []}, "query": {"parameter": []}, "body": {"parameter": []}, "cookie": {"parameter": []}, "auth": {"type": "<PERSON><PERSON><PERSON>", "kv": {"key": "", "value": "", "in": ""}, "bearer": {"key": ""}, "basic": {"username": "", "password": ""}, "digest": {"username": "", "password": "", "realm": "", "nonce": "", "algorithm": "", "qop": "", "nc": "", "cnonce": "", "opaque": ""}, "hawk": {"authId": "", "authKey": "", "algorithm": "", "user": "", "nonce": "", "extraData": "", "default": "", "delegation": "", "timestamp": "", "includePayloadHash": false}, "awsv4": {"accessKey": "", "secretKey": "", "region": "", "service": "", "sessionToken": "", "addAuthDataToQuery": false}, "ntlm": {"username": "", "password": "", "domain": "", "workstation": "", "disableRetryRequest": false}, "edgegrid": {"accessToken": "", "clientToken": "", "clientSecret": "", "nonce": "", "timestamp": "", "baseURi": "", "headersToSign": ""}, "oauth1": {"consumerKey": "", "consumerSecret": "", "signatureMethod": "", "addEmptyParamsToSign": false, "includeBodyHash": false, "addParamsToHeader": false, "disableHeaderEncoding": false, "realm": "", "version": "", "nonce": "", "timestamp": "", "verifier": "", "callback": "", "tokenSecret": "", "token": ""}, "jwt": {"addTokenTo": "", "algorithm": "", "secret": "", "isSecretBase64Encoded": false, "payload": "", "headerPrefix": "", "queryParamKey": "", "header": ""}, "asap": {"alg": "", "iss": "", "aud": "", "kid": "", "privateKey": "", "sub": "", "claims": "", "exp": ""}}, "pre_tasks": [], "post_tasks": []}, "codes": [], "marks": [{"mark_id": "1", "project_id": "0", "name": "开发中", "color": "#2857FF", "is_sys_default": 1, "is_default_mark": 1}, {"mark_id": "2", "project_id": "0", "name": "已完成", "color": "#26CEA4", "is_sys_default": 1, "is_default_mark": -1}, {"mark_id": "3", "project_id": "0", "name": "需修改", "color": "#FFC01E", "is_sys_default": 1, "is_default_mark": -1}, {"mark_id": "4", "project_id": "0", "name": "已废弃", "color": "#FF2200", "is_sys_default": 1, "is_default_mark": -1}], "attributes": [], "mock_custom_rules": [], "db_link": [], "describe_library": []}, "models": [], "apis": [{"target_id": "451b2e53cd29f", "project_id": "451b2e53cd29b", "parent_id": "0", "target_type": "folder", "name": "登录模块", "version": 1, "sort": 2000, "server_id": "0", "description": "", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>", "kv": {"key": "", "value": "", "in": "header"}, "bearer": {"key": ""}, "basic": {"username": "", "password": ""}, "digest": {"username": "", "password": "", "realm": "", "nonce": "", "algorithm": "", "qop": "", "nc": "", "cnonce": "", "opaque": ""}, "hawk": {"authId": "", "authKey": "", "algorithm": "", "user": "", "nonce": "", "extraData": "", "default": "", "delegation": "", "timestamp": "", "includePayloadHash": false}, "awsv4": {"accessKey": "", "secretKey": "", "region": "", "service": "", "sessionToken": "", "addAuthDataToQuery": false}, "ntlm": {"username": "", "password": "", "domain": "", "workstation": "", "disableRetryRequest": false}, "edgegrid": {"accessToken": "", "clientToken": "", "clientSecret": "", "nonce": "", "timestamp": "", "baseURi": "", "headersToSign": ""}, "oauth1": {"consumerKey": "", "consumerSecret": "", "signatureMethod": "HMAC-SHA1", "addEmptyParamsToSign": true, "includeBodyHash": true, "addParamsToHeader": false, "disableHeaderEncoding": false, "realm": "", "version": "1.0", "nonce": "", "timestamp": "", "verifier": "", "callback": "", "tokenSecret": "", "token": ""}, "jwt": {"addTokenTo": "header", "algorithm": "HS256", "secret": "", "isSecretBase64Encoded": false, "payload": "", "headerPrefix": "Bearer", "queryParamKey": "token", "header": ""}, "asap": {"alg": "HS256", "iss": "", "aud": "", "kid": "", "privateKey": "", "sub": "", "claims": "", "exp": ""}}, "pre_tasks": [], "post_tasks": [], "body": {"parameter": []}, "header": {"parameter": []}, "query": {"parameter": []}, "cookie": {"parameter": []}}}, {"target_id": "451b2e53cd2a0", "project_id": "451b2e53cd29b", "parent_id": "0", "target_type": "folder", "name": "消息模块", "version": 1, "sort": 3000, "server_id": "0", "description": "", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>", "kv": {"key": "", "value": "", "in": "header"}, "bearer": {"key": ""}, "basic": {"username": "", "password": ""}, "digest": {"username": "", "password": "", "realm": "", "nonce": "", "algorithm": "", "qop": "", "nc": "", "cnonce": "", "opaque": ""}, "hawk": {"authId": "", "authKey": "", "algorithm": "", "user": "", "nonce": "", "extraData": "", "default": "", "delegation": "", "timestamp": "", "includePayloadHash": false}, "awsv4": {"accessKey": "", "secretKey": "", "region": "", "service": "", "sessionToken": "", "addAuthDataToQuery": false}, "ntlm": {"username": "", "password": "", "domain": "", "workstation": "", "disableRetryRequest": false}, "edgegrid": {"accessToken": "", "clientToken": "", "clientSecret": "", "nonce": "", "timestamp": "", "baseURi": "", "headersToSign": ""}, "oauth1": {"consumerKey": "", "consumerSecret": "", "signatureMethod": "HMAC-SHA1", "addEmptyParamsToSign": true, "includeBodyHash": true, "addParamsToHeader": false, "disableHeaderEncoding": false, "realm": "", "version": "1.0", "nonce": "", "timestamp": "", "verifier": "", "callback": "", "tokenSecret": "", "token": ""}, "jwt": {"addTokenTo": "header", "algorithm": "HS256", "secret": "", "isSecretBase64Encoded": false, "payload": "", "headerPrefix": "Bearer", "queryParamKey": "token", "header": ""}, "asap": {"alg": "HS256", "iss": "", "aud": "", "kid": "", "privateKey": "", "sub": "", "claims": "", "exp": ""}}, "pre_tasks": [], "post_tasks": [], "body": {"parameter": []}, "header": {"parameter": []}, "query": {"parameter": []}, "cookie": {"parameter": []}}}, {"target_id": "451b2e53cd2a1", "project_id": "451b2e53cd29b", "parent_id": "0", "target_type": "folder", "name": "支付模块", "version": 1, "sort": 4000, "server_id": "0", "description": "", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>", "kv": {"key": "", "value": "", "in": "header"}, "bearer": {"key": ""}, "basic": {"username": "", "password": ""}, "digest": {"username": "", "password": "", "realm": "", "nonce": "", "algorithm": "", "qop": "", "nc": "", "cnonce": "", "opaque": ""}, "hawk": {"authId": "", "authKey": "", "algorithm": "", "user": "", "nonce": "", "extraData": "", "default": "", "delegation": "", "timestamp": "", "includePayloadHash": false}, "awsv4": {"accessKey": "", "secretKey": "", "region": "", "service": "", "sessionToken": "", "addAuthDataToQuery": false}, "ntlm": {"username": "", "password": "", "domain": "", "workstation": "", "disableRetryRequest": false}, "edgegrid": {"accessToken": "", "clientToken": "", "clientSecret": "", "nonce": "", "timestamp": "", "baseURi": "", "headersToSign": ""}, "oauth1": {"consumerKey": "", "consumerSecret": "", "signatureMethod": "HMAC-SHA1", "addEmptyParamsToSign": true, "includeBodyHash": true, "addParamsToHeader": false, "disableHeaderEncoding": false, "realm": "", "version": "1.0", "nonce": "", "timestamp": "", "verifier": "", "callback": "", "tokenSecret": "", "token": ""}, "jwt": {"addTokenTo": "header", "algorithm": "HS256", "secret": "", "isSecretBase64Encoded": false, "payload": "", "headerPrefix": "Bearer", "queryParamKey": "token", "header": ""}, "asap": {"alg": "HS256", "iss": "", "aud": "", "kid": "", "privateKey": "", "sub": "", "claims": "", "exp": ""}}, "pre_tasks": [], "post_tasks": [], "body": {"parameter": []}, "header": {"parameter": []}, "query": {"parameter": []}, "cookie": {"parameter": []}}}, {"target_id": "451b2e53cd2a2", "project_id": "451b2e53cd29b", "parent_id": "0", "target_type": "folder", "name": "朋友模块", "version": 1, "sort": 5000, "server_id": "0", "description": "", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>", "kv": {"key": "", "value": "", "in": "header"}, "bearer": {"key": ""}, "basic": {"username": "", "password": ""}, "digest": {"username": "", "password": "", "realm": "", "nonce": "", "algorithm": "", "qop": "", "nc": "", "cnonce": "", "opaque": ""}, "hawk": {"authId": "", "authKey": "", "algorithm": "", "user": "", "nonce": "", "extraData": "", "default": "", "delegation": "", "timestamp": "", "includePayloadHash": false}, "awsv4": {"accessKey": "", "secretKey": "", "region": "", "service": "", "sessionToken": "", "addAuthDataToQuery": false}, "ntlm": {"username": "", "password": "", "domain": "", "workstation": "", "disableRetryRequest": false}, "edgegrid": {"accessToken": "", "clientToken": "", "clientSecret": "", "nonce": "", "timestamp": "", "baseURi": "", "headersToSign": ""}, "oauth1": {"consumerKey": "", "consumerSecret": "", "signatureMethod": "HMAC-SHA1", "addEmptyParamsToSign": true, "includeBodyHash": true, "addParamsToHeader": false, "disableHeaderEncoding": false, "realm": "", "version": "1.0", "nonce": "", "timestamp": "", "verifier": "", "callback": "", "tokenSecret": "", "token": ""}, "jwt": {"addTokenTo": "header", "algorithm": "HS256", "secret": "", "isSecretBase64Encoded": false, "payload": "", "headerPrefix": "Bearer", "queryParamKey": "token", "header": ""}, "asap": {"alg": "HS256", "iss": "", "aud": "", "kid": "", "privateKey": "", "sub": "", "claims": "", "exp": ""}}, "pre_tasks": [], "post_tasks": [], "body": {"parameter": []}, "header": {"parameter": []}, "query": {"parameter": []}, "cookie": {"parameter": []}}}, {"target_id": "451b2e53cd2a3", "project_id": "451b2e53cd29b", "parent_id": "0", "target_type": "folder", "name": "群聊模块", "version": 1, "sort": 6000, "server_id": "0", "description": "", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>", "kv": {"key": "", "value": "", "in": "header"}, "bearer": {"key": ""}, "basic": {"username": "", "password": ""}, "digest": {"username": "", "password": "", "realm": "", "nonce": "", "algorithm": "", "qop": "", "nc": "", "cnonce": "", "opaque": ""}, "hawk": {"authId": "", "authKey": "", "algorithm": "", "user": "", "nonce": "", "extraData": "", "default": "", "delegation": "", "timestamp": "", "includePayloadHash": false}, "awsv4": {"accessKey": "", "secretKey": "", "region": "", "service": "", "sessionToken": "", "addAuthDataToQuery": false}, "ntlm": {"username": "", "password": "", "domain": "", "workstation": "", "disableRetryRequest": false}, "edgegrid": {"accessToken": "", "clientToken": "", "clientSecret": "", "nonce": "", "timestamp": "", "baseURi": "", "headersToSign": ""}, "oauth1": {"consumerKey": "", "consumerSecret": "", "signatureMethod": "HMAC-SHA1", "addEmptyParamsToSign": true, "includeBodyHash": true, "addParamsToHeader": false, "disableHeaderEncoding": false, "realm": "", "version": "1.0", "nonce": "", "timestamp": "", "verifier": "", "callback": "", "tokenSecret": "", "token": ""}, "jwt": {"addTokenTo": "header", "algorithm": "HS256", "secret": "", "isSecretBase64Encoded": false, "payload": "", "headerPrefix": "Bearer", "queryParamKey": "token", "header": ""}, "asap": {"alg": "HS256", "iss": "", "aud": "", "kid": "", "privateKey": "", "sub": "", "claims": "", "exp": ""}}, "pre_tasks": [], "post_tasks": [], "body": {"parameter": []}, "header": {"parameter": []}, "query": {"parameter": []}, "cookie": {"parameter": []}}}, {"target_id": "451b2e53cd2a4", "project_id": "451b2e53cd29b", "parent_id": "0", "target_type": "folder", "name": "设备模块", "version": 1, "sort": 7000, "server_id": "0", "description": "", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>", "kv": {"key": "", "value": "", "in": "header"}, "bearer": {"key": ""}, "basic": {"username": "", "password": ""}, "digest": {"username": "", "password": "", "realm": "", "nonce": "", "algorithm": "", "qop": "", "nc": "", "cnonce": "", "opaque": ""}, "hawk": {"authId": "", "authKey": "", "algorithm": "", "user": "", "nonce": "", "extraData": "", "default": "", "delegation": "", "timestamp": "", "includePayloadHash": false}, "awsv4": {"accessKey": "", "secretKey": "", "region": "", "service": "", "sessionToken": "", "addAuthDataToQuery": false}, "ntlm": {"username": "", "password": "", "domain": "", "workstation": "", "disableRetryRequest": false}, "edgegrid": {"accessToken": "", "clientToken": "", "clientSecret": "", "nonce": "", "timestamp": "", "baseURi": "", "headersToSign": ""}, "oauth1": {"consumerKey": "", "consumerSecret": "", "signatureMethod": "HMAC-SHA1", "addEmptyParamsToSign": true, "includeBodyHash": true, "addParamsToHeader": false, "disableHeaderEncoding": false, "realm": "", "version": "1.0", "nonce": "", "timestamp": "", "verifier": "", "callback": "", "tokenSecret": "", "token": ""}, "jwt": {"addTokenTo": "header", "algorithm": "HS256", "secret": "", "isSecretBase64Encoded": false, "payload": "", "headerPrefix": "Bearer", "queryParamKey": "token", "header": ""}, "asap": {"alg": "HS256", "iss": "", "aud": "", "kid": "", "privateKey": "", "sub": "", "claims": "", "exp": ""}}, "pre_tasks": [], "post_tasks": [], "body": {"parameter": []}, "header": {"parameter": []}, "query": {"parameter": []}, "cookie": {"parameter": []}}}, {"target_id": "451b2e53cd2a5", "project_id": "451b2e53cd29b", "parent_id": "0", "target_type": "folder", "name": "用户模块", "version": 1, "sort": 8000, "server_id": "0", "description": "", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>", "kv": {"key": "", "value": "", "in": "header"}, "bearer": {"key": ""}, "basic": {"username": "", "password": ""}, "digest": {"username": "", "password": "", "realm": "", "nonce": "", "algorithm": "", "qop": "", "nc": "", "cnonce": "", "opaque": ""}, "hawk": {"authId": "", "authKey": "", "algorithm": "", "user": "", "nonce": "", "extraData": "", "default": "", "delegation": "", "timestamp": "", "includePayloadHash": false}, "awsv4": {"accessKey": "", "secretKey": "", "region": "", "service": "", "sessionToken": "", "addAuthDataToQuery": false}, "ntlm": {"username": "", "password": "", "domain": "", "workstation": "", "disableRetryRequest": false}, "edgegrid": {"accessToken": "", "clientToken": "", "clientSecret": "", "nonce": "", "timestamp": "", "baseURi": "", "headersToSign": ""}, "oauth1": {"consumerKey": "", "consumerSecret": "", "signatureMethod": "HMAC-SHA1", "addEmptyParamsToSign": true, "includeBodyHash": true, "addParamsToHeader": false, "disableHeaderEncoding": false, "realm": "", "version": "1.0", "nonce": "", "timestamp": "", "verifier": "", "callback": "", "tokenSecret": "", "token": ""}, "jwt": {"addTokenTo": "header", "algorithm": "HS256", "secret": "", "isSecretBase64Encoded": false, "payload": "", "headerPrefix": "Bearer", "queryParamKey": "token", "header": ""}, "asap": {"alg": "HS256", "iss": "", "aud": "", "kid": "", "privateKey": "", "sub": "", "claims": "", "exp": ""}}, "pre_tasks": [], "post_tasks": [], "body": {"parameter": []}, "header": {"parameter": []}, "query": {"parameter": []}, "cookie": {"parameter": []}}}, {"target_id": "451b2e53cd2a6", "project_id": "451b2e53cd29b", "parent_id": "451b2e53cd2a1", "target_type": "api", "name": "生成自定义二维码", "version": 1, "sort": 1000, "method": "POST", "url": "/pay/GeneratePayQCode?key={{TOKEN_KEY}}", "protocol": "http/1.1", "mark_id": "1", "description": "", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>", "kv": {"key": "", "value": "", "in": "header"}, "bearer": {"key": ""}, "basic": {"username": "", "password": ""}, "digest": {"username": "", "password": "", "realm": "", "nonce": "", "algorithm": "", "qop": "", "nc": "", "cnonce": "", "opaque": ""}, "hawk": {"authId": "", "authKey": "", "algorithm": "", "user": "", "nonce": "", "extraData": "", "default": "", "delegation": "", "timestamp": "", "includePayloadHash": false}, "awsv4": {"accessKey": "", "secretKey": "", "region": "", "service": "", "sessionToken": "", "addAuthDataToQuery": false}, "ntlm": {"username": "", "password": "", "domain": "", "workstation": "", "disableRetryRequest": false}, "edgegrid": {"accessToken": "", "clientToken": "", "clientSecret": "", "nonce": "", "timestamp": "", "baseURi": "", "headersToSign": ""}, "oauth1": {"consumerKey": "", "consumerSecret": "", "signatureMethod": "HMAC-SHA1", "addEmptyParamsToSign": true, "includeBodyHash": true, "addParamsToHeader": false, "disableHeaderEncoding": false, "realm": "", "version": "1.0", "nonce": "", "timestamp": "", "verifier": "", "callback": "", "tokenSecret": "", "token": ""}, "jwt": {"addTokenTo": "header", "algorithm": "HS256", "secret": "", "isSecretBase64Encoded": false, "payload": "", "headerPrefix": "Bearer", "queryParamKey": "token", "header": ""}, "asap": {"alg": "HS256", "iss": "", "aud": "", "kid": "", "privateKey": "", "sub": "", "claims": "", "exp": ""}}, "body": {"mode": "json", "parameter": [], "raw": "{\n\t\"Money\": \"999\",\n\t\"Name\": \"明月清风\"\n}", "raw_parameter": [], "raw_schema": {"type": "object", "properties": {}}, "binary": {}}, "pre_tasks": [], "post_tasks": [{"type": "customScript", "enabled": 1, "data": "let res = pm.response.json();\nif (res[\"Code\"] != 200) {\n    return;\n}\nlet payUrl = res[\"Data\"][\"pay_url\"];\nconsole.log(payUrl);\n\nlet apiUrl = \"https://api.caoliao.net/api/qrcode/code?text=\" + payUrl;\npm.sendRequest(apiUrl, function (err, response) {\n    const html = response.stream.toString();\n    // qr.api.cli.im/newqr/create?data=123&kid=cliim&key=4b243134fc1d98a864b053ae488211c6\n    const regex = /src=\\\"(\\/\\/qr\\.api\\.cli\\.im\\/newqr\\/create\\?data=.*?&kid=cliim&key=.*?)\\\"/;\n    const match = html.match(regex);\n    if (!match) {\n        return;\n    }\n    console.log(match);\n    const qrUrl = \"https:\" + match[1];\n    console.log(qrUrl);\n\n    const template = `<html><img height=\"200px\" width=\"200px\" src=\"{{imgTemplate}}\" /></html>`;\n    pm.visualizer.set(template, {\n        imgTemplate: qrUrl,\n    });\n});\n", "name": "自定义脚本", "id": "451b2e57cd2cd"}], "header": {"parameter": []}, "query": {"query_add_equal": 1, "parameter": [{"param_id": "451b2e57cd2ce", "description": "", "field_type": "String", "is_checked": 1, "key": "key", "not_null": -1, "value": "{{TOKEN_KEY}}"}]}, "cookie": {"parameter": []}, "restful": {"parameter": []}}, "response": {"example": [{"example_id": "451b2e57cd2cf", "raw": "", "raw_parameter": [], "expect": {"code": "200", "content_type": "json", "is_default": 1, "mock": "", "name": "成功", "schema": {"type": "object", "properties": {}}, "verify_type": "schema"}}], "is_check_result": 1}, "mock_server_enable": -1, "attribute_info": {}, "tags": []}, {"target_id": "451b2e53cd2a7", "project_id": "451b2e53cd29b", "parent_id": "451b2e53cd2a5", "target_type": "api", "name": "获取个人资料信息", "version": 1, "sort": 1000, "method": "GET", "url": "/user/GetProfile?key={{TOKEN_KEY}}", "protocol": "http/1.1", "mark_id": "1", "description": "", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>", "kv": {"key": "", "value": "", "in": "header"}, "bearer": {"key": ""}, "basic": {"username": "", "password": ""}, "digest": {"username": "", "password": "", "realm": "", "nonce": "", "algorithm": "", "qop": "", "nc": "", "cnonce": "", "opaque": ""}, "hawk": {"authId": "", "authKey": "", "algorithm": "", "user": "", "nonce": "", "extraData": "", "default": "", "delegation": "", "timestamp": "", "includePayloadHash": false}, "awsv4": {"accessKey": "", "secretKey": "", "region": "", "service": "", "sessionToken": "", "addAuthDataToQuery": false}, "ntlm": {"username": "", "password": "", "domain": "", "workstation": "", "disableRetryRequest": false}, "edgegrid": {"accessToken": "", "clientToken": "", "clientSecret": "", "nonce": "", "timestamp": "", "baseURi": "", "headersToSign": ""}, "oauth1": {"consumerKey": "", "consumerSecret": "", "signatureMethod": "HMAC-SHA1", "addEmptyParamsToSign": true, "includeBodyHash": true, "addParamsToHeader": false, "disableHeaderEncoding": false, "realm": "", "version": "1.0", "nonce": "", "timestamp": "", "verifier": "", "callback": "", "tokenSecret": "", "token": ""}, "jwt": {"addTokenTo": "header", "algorithm": "HS256", "secret": "", "isSecretBase64Encoded": false, "payload": "", "headerPrefix": "Bearer", "queryParamKey": "token", "header": ""}, "asap": {"alg": "HS256", "iss": "", "aud": "", "kid": "", "privateKey": "", "sub": "", "claims": "", "exp": ""}}, "body": {"mode": "json", "parameter": [], "raw": "", "raw_parameter": [], "raw_schema": {"type": "object", "properties": {}}, "binary": {}}, "pre_tasks": [], "post_tasks": [{"type": "customScript", "enabled": 1, "data": "let res = pm.response.json();\nif (res[\"Code\"] != 200 || !res[\"Data\"]) {\n    return;\n}\nlet wxid = res[\"Data\"][\"userInfo\"][\"userName\"][\"str\"];\nconsole.log(wxid);\npm.environment.set(\"Wxid\", wxid);\n", "name": "自定义脚本", "id": "451b2e57cd2d0"}], "header": {"parameter": []}, "query": {"query_add_equal": 1, "parameter": [{"param_id": "451b2e57cd2d1", "description": "", "field_type": "String", "is_checked": 1, "key": "key", "not_null": -1, "value": "{{TOKEN_KEY}}"}]}, "cookie": {"parameter": []}, "restful": {"parameter": []}}, "response": {"example": [{"example_id": "451b2e57cd2d2", "raw": "", "raw_parameter": [], "expect": {"code": "200", "content_type": "json", "is_default": 1, "mock": "", "name": "成功", "schema": {"type": "object", "properties": {}}, "verify_type": "schema"}}], "is_check_result": 1}, "mock_server_enable": -1, "attribute_info": {}, "tags": []}, {"target_id": "451b2e53cd2a8", "project_id": "451b2e53cd29b", "parent_id": "451b2e53cd29f", "target_type": "api", "name": "生成授权码1", "version": 1, "sort": 1000, "method": "POST", "url": "/login/GenAuthKey?key={{ADMIN_KEY}}", "protocol": "http/1.1", "mark_id": "1", "description": "", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>", "kv": {"key": "", "value": "", "in": "header"}, "bearer": {"key": ""}, "basic": {"username": "", "password": ""}, "digest": {"username": "", "password": "", "realm": "", "nonce": "", "algorithm": "", "qop": "", "nc": "", "cnonce": "", "opaque": ""}, "hawk": {"authId": "", "authKey": "", "algorithm": "", "user": "", "nonce": "", "extraData": "", "default": "", "delegation": "", "timestamp": "", "includePayloadHash": false}, "awsv4": {"accessKey": "", "secretKey": "", "region": "", "service": "", "sessionToken": "", "addAuthDataToQuery": false}, "ntlm": {"username": "", "password": "", "domain": "", "workstation": "", "disableRetryRequest": false}, "edgegrid": {"accessToken": "", "clientToken": "", "clientSecret": "", "nonce": "", "timestamp": "", "baseURi": "", "headersToSign": ""}, "oauth1": {"consumerKey": "", "consumerSecret": "", "signatureMethod": "HMAC-SHA1", "addEmptyParamsToSign": true, "includeBodyHash": true, "addParamsToHeader": false, "disableHeaderEncoding": false, "realm": "", "version": "1.0", "nonce": "", "timestamp": "", "verifier": "", "callback": "", "tokenSecret": "", "token": ""}, "jwt": {"addTokenTo": "header", "algorithm": "HS256", "secret": "", "isSecretBase64Encoded": false, "payload": "", "headerPrefix": "Bearer", "queryParamKey": "token", "header": ""}, "asap": {"alg": "HS256", "iss": "", "aud": "", "kid": "", "privateKey": "", "sub": "", "claims": "", "exp": ""}}, "body": {"mode": "json", "parameter": [], "raw": "{\n\t\"Count\": 1,\n\t\"Days\": 3\n}", "raw_parameter": [], "raw_schema": {"type": "object", "properties": {}}, "binary": {}}, "pre_tasks": [], "post_tasks": [{"type": "customScript", "enabled": 1, "data": "let res = pm.response.json();\nif (res[\"Code\"] != 200 || !res[\"Data\"]) {\n    return;\n}\nlet authKeys = res[\"Data\"];\n\nlet oldKeys = pm.environment.get(\"OLD_TOKEN_KEY\");\nif (!oldKeys) {\n    oldKeys = \"[]\";\n}\n\noldKeys = JSON.parse(oldKeys);\nconsole.log(\"上一次的 TOKEN_KEY:\", oldKeys);\nconsole.log(\"新获取的 TOKEN_KEY:\", authKeys);\n\noldKeys = [...oldKeys, ...authKeys];\noldKeys = JSON.stringify(oldKeys);\npm.environment.set(\"OLD_TOKEN_KEY\", oldKeys);\n\npm.environment.set(\"TOKEN_KEY\", authKeys[0]);\n", "name": "自定义脚本", "id": "45beae2fcd004"}], "header": {"parameter": []}, "query": {"query_add_equal": 1, "parameter": [{"param_id": "451b2e57cd2d3", "description": "", "field_type": "String", "is_checked": 1, "key": "key", "not_null": -1, "value": "{{ADMIN_KEY}}"}]}, "cookie": {"parameter": []}, "restful": {"parameter": []}}, "response": {"example": [{"example_id": "451b2e57cd2d4", "raw": "", "raw_parameter": [], "expect": {"code": "200", "content_type": "json", "is_default": 1, "mock": "", "name": "成功", "schema": {"type": "object", "properties": {}}, "verify_type": "schema"}}], "is_check_result": 1}, "mock_server_enable": -1, "attribute_info": {}, "tags": []}, {"target_id": "451b2e53cd2a9", "project_id": "451b2e53cd29b", "parent_id": "451b2e53cd2a0", "target_type": "websocket", "name": "同步消息", "version": 1, "sort": 1000, "method": "Raw", "url": "ws://127.0.0.1:8848/ws/GetSyncMsg", "mark_id": "1", "description": "", "request": {"header": {"parameter": []}, "query": {"parameter": [{"param_id": "451b2e5bcd2d5", "description": "", "field_type": "String", "is_checked": 1, "key": "key", "not_null": 1, "value": "{{TOKEN_KEY}}"}]}, "event": {"parameter": []}, "message": {"mode": "text", "raw": "", "raw_parameter": [], "raw_schema": {"type": "object"}}}, "config": {"socket_version": "v4", "shake_hands_path": "", "shake_hands_timeout": 0, "reconnect_num": 5, "reconnect_time": 5000, "information_size": 5}}, {"target_id": "451b2e53cd2aa", "project_id": "451b2e53cd29b", "parent_id": "451b2e53cd2a2", "target_type": "api", "name": "获取好友列表", "version": 1, "sort": 1000, "method": "GET", "url": "/friend/GetFriendList?key={{TOKEN_KEY}}", "protocol": "http/1.1", "mark_id": "1", "description": "", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>", "kv": {"key": "", "value": "", "in": "header"}, "bearer": {"key": ""}, "basic": {"username": "", "password": ""}, "digest": {"username": "", "password": "", "realm": "", "nonce": "", "algorithm": "", "qop": "", "nc": "", "cnonce": "", "opaque": ""}, "hawk": {"authId": "", "authKey": "", "algorithm": "", "user": "", "nonce": "", "extraData": "", "default": "", "delegation": "", "timestamp": "", "includePayloadHash": false}, "awsv4": {"accessKey": "", "secretKey": "", "region": "", "service": "", "sessionToken": "", "addAuthDataToQuery": false}, "ntlm": {"username": "", "password": "", "domain": "", "workstation": "", "disableRetryRequest": false}, "edgegrid": {"accessToken": "", "clientToken": "", "clientSecret": "", "nonce": "", "timestamp": "", "baseURi": "", "headersToSign": ""}, "oauth1": {"consumerKey": "", "consumerSecret": "", "signatureMethod": "HMAC-SHA1", "addEmptyParamsToSign": true, "includeBodyHash": true, "addParamsToHeader": false, "disableHeaderEncoding": false, "realm": "", "version": "1.0", "nonce": "", "timestamp": "", "verifier": "", "callback": "", "tokenSecret": "", "token": ""}, "jwt": {"addTokenTo": "header", "algorithm": "HS256", "secret": "", "isSecretBase64Encoded": false, "payload": "", "headerPrefix": "Bearer", "queryParamKey": "token", "header": ""}, "asap": {"alg": "HS256", "iss": "", "aud": "", "kid": "", "privateKey": "", "sub": "", "claims": "", "exp": ""}}, "body": {"mode": "json", "parameter": [], "raw": "", "raw_parameter": [], "raw_schema": {"type": "object", "properties": {}}, "binary": {}}, "pre_tasks": [], "post_tasks": [], "header": {"parameter": []}, "query": {"query_add_equal": 1, "parameter": [{"param_id": "451b2e5bcd2dc", "description": "", "field_type": "String", "is_checked": 1, "key": "key", "not_null": -1, "value": "{{TOKEN_KEY}}"}]}, "cookie": {"parameter": []}, "restful": {"parameter": []}}, "response": {"example": [{"example_id": "451b2e5bcd2dd", "raw": "", "raw_parameter": [], "expect": {"code": "200", "content_type": "json", "is_default": 1, "mock": "", "name": "成功", "schema": {"type": "object", "properties": {}}, "verify_type": "schema"}}], "is_check_result": 1}, "mock_server_enable": -1, "attribute_info": {}, "tags": []}, {"target_id": "451b2e53cd2ab", "project_id": "451b2e53cd29b", "parent_id": "451b2e53cd2a4", "target_type": "api", "name": "获取在线设备信息", "version": 1, "sort": 1000, "method": "GET", "url": "/equipment/GetOnlineInfo?key={{TOKEN_KEY}}", "protocol": "http/1.1", "mark_id": "1", "description": "", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>", "kv": {"key": "", "value": "", "in": "header"}, "bearer": {"key": ""}, "basic": {"username": "", "password": ""}, "digest": {"username": "", "password": "", "realm": "", "nonce": "", "algorithm": "", "qop": "", "nc": "", "cnonce": "", "opaque": ""}, "hawk": {"authId": "", "authKey": "", "algorithm": "", "user": "", "nonce": "", "extraData": "", "default": "", "delegation": "", "timestamp": "", "includePayloadHash": false}, "awsv4": {"accessKey": "", "secretKey": "", "region": "", "service": "", "sessionToken": "", "addAuthDataToQuery": false}, "ntlm": {"username": "", "password": "", "domain": "", "workstation": "", "disableRetryRequest": false}, "edgegrid": {"accessToken": "", "clientToken": "", "clientSecret": "", "nonce": "", "timestamp": "", "baseURi": "", "headersToSign": ""}, "oauth1": {"consumerKey": "", "consumerSecret": "", "signatureMethod": "HMAC-SHA1", "addEmptyParamsToSign": true, "includeBodyHash": true, "addParamsToHeader": false, "disableHeaderEncoding": false, "realm": "", "version": "1.0", "nonce": "", "timestamp": "", "verifier": "", "callback": "", "tokenSecret": "", "token": ""}, "jwt": {"addTokenTo": "header", "algorithm": "HS256", "secret": "", "isSecretBase64Encoded": false, "payload": "", "headerPrefix": "Bearer", "queryParamKey": "token", "header": ""}, "asap": {"alg": "HS256", "iss": "", "aud": "", "kid": "", "privateKey": "", "sub": "", "claims": "", "exp": ""}}, "body": {"mode": "json", "parameter": [], "raw": "", "raw_parameter": [], "raw_schema": {"type": "object", "properties": {}}, "binary": {}}, "pre_tasks": [], "post_tasks": [], "header": {"parameter": []}, "query": {"query_add_equal": 1, "parameter": [{"param_id": "451b2e5bcd2de", "description": "", "field_type": "String", "is_checked": 1, "key": "key", "not_null": -1, "value": "{{TOKEN_KEY}}"}]}, "cookie": {"parameter": []}, "restful": {"parameter": []}}, "response": {"example": [{"example_id": "451b2e5bcd2df", "raw": "", "raw_parameter": [], "expect": {"code": "200", "content_type": "json", "is_default": 1, "mock": "", "name": "成功", "schema": {"type": "object", "properties": {}}, "verify_type": "schema"}}], "is_check_result": 1}, "mock_server_enable": -1, "attribute_info": {}, "tags": []}, {"target_id": "451b2e53cd2ac", "project_id": "451b2e53cd29b", "parent_id": "451b2e53cd2a0", "target_type": "api", "name": "同步历史消息", "version": 1, "sort": 2000, "method": "POST", "url": "/message/NewSyncHistoryMessage?key={{TOKEN_KEY}}", "protocol": "http/1.1", "mark_id": "1", "description": "", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>", "kv": {"key": "", "value": "", "in": "header"}, "bearer": {"key": ""}, "basic": {"username": "", "password": ""}, "digest": {"username": "", "password": "", "realm": "", "nonce": "", "algorithm": "", "qop": "", "nc": "", "cnonce": "", "opaque": ""}, "hawk": {"authId": "", "authKey": "", "algorithm": "", "user": "", "nonce": "", "extraData": "", "default": "", "delegation": "", "timestamp": "", "includePayloadHash": false}, "awsv4": {"accessKey": "", "secretKey": "", "region": "", "service": "", "sessionToken": "", "addAuthDataToQuery": false}, "ntlm": {"username": "", "password": "", "domain": "", "workstation": "", "disableRetryRequest": false}, "edgegrid": {"accessToken": "", "clientToken": "", "clientSecret": "", "nonce": "", "timestamp": "", "baseURi": "", "headersToSign": ""}, "oauth1": {"consumerKey": "", "consumerSecret": "", "signatureMethod": "HMAC-SHA1", "addEmptyParamsToSign": true, "includeBodyHash": true, "addParamsToHeader": false, "disableHeaderEncoding": false, "realm": "", "version": "1.0", "nonce": "", "timestamp": "", "verifier": "", "callback": "", "tokenSecret": "", "token": ""}, "jwt": {"addTokenTo": "header", "algorithm": "HS256", "secret": "", "isSecretBase64Encoded": false, "payload": "", "headerPrefix": "Bearer", "queryParamKey": "token", "header": ""}, "asap": {"alg": "HS256", "iss": "", "aud": "", "kid": "", "privateKey": "", "sub": "", "claims": "", "exp": ""}}, "body": {"mode": "json", "parameter": [], "raw": "", "raw_parameter": [], "raw_schema": {"type": "object", "properties": {}}, "binary": {}}, "pre_tasks": [], "post_tasks": [], "header": {"parameter": []}, "query": {"query_add_equal": 1, "parameter": [{"param_id": "451b2e5bcd2d6", "description": "", "field_type": "String", "is_checked": 1, "key": "key", "not_null": -1, "value": "{{TOKEN_KEY}}"}]}, "cookie": {"parameter": []}, "restful": {"parameter": []}}, "response": {"example": [{"example_id": "451b2e5bcd2d7", "raw": "", "raw_parameter": [], "expect": {"code": "200", "content_type": "json", "is_default": 1, "mock": "", "name": "成功", "schema": {"type": "object", "properties": {}}, "verify_type": "schema"}}], "is_check_result": 1}, "mock_server_enable": -1, "attribute_info": {}, "tags": []}, {"target_id": "451b2e53cd2ad", "project_id": "451b2e53cd29b", "parent_id": "451b2e53cd29f", "target_type": "api", "name": "生成授权码2", "version": 1, "sort": 2000, "method": "GET", "url": "/login/GenAuthKey2?key={{ADMIN_KEY}}&count=1&days=3", "protocol": "http/1.1", "mark_id": "1", "description": "", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>", "kv": {"key": "", "value": "", "in": "header"}, "bearer": {"key": ""}, "basic": {"username": "", "password": ""}, "digest": {"username": "", "password": "", "realm": "", "nonce": "", "algorithm": "", "qop": "", "nc": "", "cnonce": "", "opaque": ""}, "hawk": {"authId": "", "authKey": "", "algorithm": "", "user": "", "nonce": "", "extraData": "", "default": "", "delegation": "", "timestamp": "", "includePayloadHash": false}, "awsv4": {"accessKey": "", "secretKey": "", "region": "", "service": "", "sessionToken": "", "addAuthDataToQuery": false}, "ntlm": {"username": "", "password": "", "domain": "", "workstation": "", "disableRetryRequest": false}, "edgegrid": {"accessToken": "", "clientToken": "", "clientSecret": "", "nonce": "", "timestamp": "", "baseURi": "", "headersToSign": ""}, "oauth1": {"consumerKey": "", "consumerSecret": "", "signatureMethod": "HMAC-SHA1", "addEmptyParamsToSign": true, "includeBodyHash": true, "addParamsToHeader": false, "disableHeaderEncoding": false, "realm": "", "version": "1.0", "nonce": "", "timestamp": "", "verifier": "", "callback": "", "tokenSecret": "", "token": ""}, "jwt": {"addTokenTo": "header", "algorithm": "HS256", "secret": "", "isSecretBase64Encoded": false, "payload": "", "headerPrefix": "Bearer", "queryParamKey": "token", "header": ""}, "asap": {"alg": "HS256", "iss": "", "aud": "", "kid": "", "privateKey": "", "sub": "", "claims": "", "exp": ""}}, "body": {"mode": "none", "parameter": [], "raw": "", "raw_parameter": [], "raw_schema": {"type": "object", "properties": {}}, "binary": {}}, "pre_tasks": [], "post_tasks": [{"type": "customScript", "enabled": 1, "data": "let res = pm.response.json();\nif (res[\"Code\"] != 200 || !res[\"Data\"]) {\n    return;\n}\nlet authKeys = res[\"Data\"];\n\nlet oldKeys = pm.environment.get(\"OLD_TOKEN_KEY\");\nif (!oldKeys) {\n    oldKeys = \"[]\";\n}\n\noldKeys = JSON.parse(oldKeys);\nconsole.log(\"上一次的 TOKEN_KEY:\", oldKeys);\nconsole.log(\"新获取的 TOKEN_KEY:\", authKeys);\n\noldKeys = [...oldKeys, ...authKeys];\noldKeys = JSON.stringify(oldKeys);\npm.environment.set(\"OLD_TOKEN_KEY\", oldKeys);\n\npm.environment.set(\"TOKEN_KEY\", authKeys[0]);\n", "name": "自定义脚本", "id": "45d066a3cd004"}], "header": {"parameter": []}, "query": {"query_add_equal": 1, "parameter": [{"param_id": "451b2e5bcd2d8", "description": "", "field_type": "String", "is_checked": 1, "key": "key", "not_null": -1, "value": "{{ADMIN_KEY}}"}, {"param_id": "451b2e5bcd2d9", "description": "", "field_type": "Integer", "is_checked": 1, "key": "count", "not_null": -1, "value": "1"}, {"param_id": "451b2e5bcd2da", "description": "", "field_type": "Integer", "is_checked": 1, "key": "days", "not_null": -1, "value": "3"}]}, "cookie": {"parameter": []}, "restful": {"parameter": []}}, "response": {"example": [{"example_id": "451b2e5bcd2db", "raw": "", "raw_parameter": [], "expect": {"code": "200", "content_type": "json", "is_default": 1, "mock": "", "name": "成功", "schema": {"type": "object", "properties": {}}, "verify_type": "schema"}}], "is_check_result": 1}, "mock_server_enable": -1, "attribute_info": {}, "tags": []}, {"target_id": "451b2e53cd2ae", "project_id": "451b2e53cd29b", "parent_id": "451b2e53cd2a2", "target_type": "api", "name": "获取关注的公众号列表", "version": 1, "sort": 2000, "method": "GET", "url": "/friend/GetGHList?key={{TOKEN_KEY}}", "protocol": "http/1.1", "mark_id": "1", "description": "", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>", "kv": {"key": "", "value": "", "in": "header"}, "bearer": {"key": ""}, "basic": {"username": "", "password": ""}, "digest": {"username": "", "password": "", "realm": "", "nonce": "", "algorithm": "", "qop": "", "nc": "", "cnonce": "", "opaque": ""}, "hawk": {"authId": "", "authKey": "", "algorithm": "", "user": "", "nonce": "", "extraData": "", "default": "", "delegation": "", "timestamp": "", "includePayloadHash": false}, "awsv4": {"accessKey": "", "secretKey": "", "region": "", "service": "", "sessionToken": "", "addAuthDataToQuery": false}, "ntlm": {"username": "", "password": "", "domain": "", "workstation": "", "disableRetryRequest": false}, "edgegrid": {"accessToken": "", "clientToken": "", "clientSecret": "", "nonce": "", "timestamp": "", "baseURi": "", "headersToSign": ""}, "oauth1": {"consumerKey": "", "consumerSecret": "", "signatureMethod": "HMAC-SHA1", "addEmptyParamsToSign": true, "includeBodyHash": true, "addParamsToHeader": false, "disableHeaderEncoding": false, "realm": "", "version": "1.0", "nonce": "", "timestamp": "", "verifier": "", "callback": "", "tokenSecret": "", "token": ""}, "jwt": {"addTokenTo": "header", "algorithm": "HS256", "secret": "", "isSecretBase64Encoded": false, "payload": "", "headerPrefix": "Bearer", "queryParamKey": "token", "header": ""}, "asap": {"alg": "HS256", "iss": "", "aud": "", "kid": "", "privateKey": "", "sub": "", "claims": "", "exp": ""}}, "body": {"mode": "json", "parameter": [], "raw": "", "raw_parameter": [], "raw_schema": {"type": "object", "properties": {}}, "binary": {}}, "pre_tasks": [], "post_tasks": [], "header": {"parameter": []}, "query": {"query_add_equal": 1, "parameter": [{"param_id": "451b2e5bcd2e0", "description": "", "field_type": "String", "is_checked": 1, "key": "key", "not_null": -1, "value": "{{TOKEN_KEY}}"}]}, "cookie": {"parameter": []}, "restful": {"parameter": []}}, "response": {"example": [{"example_id": "451b2e5bcd2e1", "raw": "", "raw_parameter": [], "expect": {"code": "200", "content_type": "json", "is_default": 1, "mock": "", "name": "成功", "schema": {"type": "object", "properties": {}}, "verify_type": "schema"}}], "is_check_result": 1}, "mock_server_enable": -1, "attribute_info": {}, "tags": []}, {"target_id": "451b2e53cd2af", "project_id": "451b2e53cd29b", "parent_id": "451b2e53cd2a4", "target_type": "api", "name": "获取安全设备列表", "version": 1, "sort": 2000, "method": "POST", "url": "/equipment/GetSafetyInfo?key={{TOKEN_KEY}}", "protocol": "http/1.1", "mark_id": "1", "description": "", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>", "kv": {"key": "", "value": "", "in": "header"}, "bearer": {"key": ""}, "basic": {"username": "", "password": ""}, "digest": {"username": "", "password": "", "realm": "", "nonce": "", "algorithm": "", "qop": "", "nc": "", "cnonce": "", "opaque": ""}, "hawk": {"authId": "", "authKey": "", "algorithm": "", "user": "", "nonce": "", "extraData": "", "default": "", "delegation": "", "timestamp": "", "includePayloadHash": false}, "awsv4": {"accessKey": "", "secretKey": "", "region": "", "service": "", "sessionToken": "", "addAuthDataToQuery": false}, "ntlm": {"username": "", "password": "", "domain": "", "workstation": "", "disableRetryRequest": false}, "edgegrid": {"accessToken": "", "clientToken": "", "clientSecret": "", "nonce": "", "timestamp": "", "baseURi": "", "headersToSign": ""}, "oauth1": {"consumerKey": "", "consumerSecret": "", "signatureMethod": "HMAC-SHA1", "addEmptyParamsToSign": true, "includeBodyHash": true, "addParamsToHeader": false, "disableHeaderEncoding": false, "realm": "", "version": "1.0", "nonce": "", "timestamp": "", "verifier": "", "callback": "", "tokenSecret": "", "token": ""}, "jwt": {"addTokenTo": "header", "algorithm": "HS256", "secret": "", "isSecretBase64Encoded": false, "payload": "", "headerPrefix": "Bearer", "queryParamKey": "token", "header": ""}, "asap": {"alg": "HS256", "iss": "", "aud": "", "kid": "", "privateKey": "", "sub": "", "claims": "", "exp": ""}}, "body": {"mode": "json", "parameter": [], "raw": "", "raw_parameter": [], "raw_schema": {"type": "object", "properties": {}}, "binary": {}}, "pre_tasks": [], "post_tasks": [], "header": {"parameter": []}, "query": {"query_add_equal": 1, "parameter": [{"param_id": "451b2e5bcd2ef", "description": "", "field_type": "String", "is_checked": 1, "key": "key", "not_null": -1, "value": "{{TOKEN_KEY}}"}]}, "cookie": {"parameter": []}, "restful": {"parameter": []}}, "response": {"example": [{"example_id": "451b2e5bcd2f0", "raw": "", "raw_parameter": [], "expect": {"code": "200", "content_type": "json", "is_default": 1, "mock": "", "name": "成功", "schema": {"type": "object", "properties": {}}, "verify_type": "schema"}}], "is_check_result": 1}, "mock_server_enable": -1, "attribute_info": {}, "tags": []}, {"target_id": "451b2e53cd2b0", "project_id": "451b2e53cd29b", "parent_id": "451b2e53cd2a2", "target_type": "api", "name": "获取保存的群聊列表", "version": 1, "sort": 3000, "method": "GET", "url": "/friend/GroupList?key={{TOKEN_KEY}}", "protocol": "http/1.1", "mark_id": "1", "description": "", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>", "kv": {"key": "", "value": "", "in": "header"}, "bearer": {"key": ""}, "basic": {"username": "", "password": ""}, "digest": {"username": "", "password": "", "realm": "", "nonce": "", "algorithm": "", "qop": "", "nc": "", "cnonce": "", "opaque": ""}, "hawk": {"authId": "", "authKey": "", "algorithm": "", "user": "", "nonce": "", "extraData": "", "default": "", "delegation": "", "timestamp": "", "includePayloadHash": false}, "awsv4": {"accessKey": "", "secretKey": "", "region": "", "service": "", "sessionToken": "", "addAuthDataToQuery": false}, "ntlm": {"username": "", "password": "", "domain": "", "workstation": "", "disableRetryRequest": false}, "edgegrid": {"accessToken": "", "clientToken": "", "clientSecret": "", "nonce": "", "timestamp": "", "baseURi": "", "headersToSign": ""}, "oauth1": {"consumerKey": "", "consumerSecret": "", "signatureMethod": "HMAC-SHA1", "addEmptyParamsToSign": true, "includeBodyHash": true, "addParamsToHeader": false, "disableHeaderEncoding": false, "realm": "", "version": "1.0", "nonce": "", "timestamp": "", "verifier": "", "callback": "", "tokenSecret": "", "token": ""}, "jwt": {"addTokenTo": "header", "algorithm": "HS256", "secret": "", "isSecretBase64Encoded": false, "payload": "", "headerPrefix": "Bearer", "queryParamKey": "token", "header": ""}, "asap": {"alg": "HS256", "iss": "", "aud": "", "kid": "", "privateKey": "", "sub": "", "claims": "", "exp": ""}}, "body": {"mode": "json", "parameter": [], "raw": "", "raw_parameter": [], "raw_schema": {"type": "object", "properties": {}}, "binary": {}}, "pre_tasks": [], "post_tasks": [], "header": {"parameter": []}, "query": {"query_add_equal": 1, "parameter": [{"param_id": "451b2e5bcd2e2", "description": "", "field_type": "String", "is_checked": 1, "key": "key", "not_null": -1, "value": "{{TOKEN_KEY}}"}]}, "cookie": {"parameter": []}, "restful": {"parameter": []}}, "response": {"example": [{"example_id": "451b2e5bcd2e3", "raw": "", "raw_parameter": [], "expect": {"code": "200", "content_type": "json", "is_default": 1, "mock": "", "name": "成功", "schema": {"type": "object", "properties": {}}, "verify_type": "schema"}}], "is_check_result": 1}, "mock_server_enable": -1, "attribute_info": {}, "tags": []}, {"target_id": "451b2e53cd2b1", "project_id": "451b2e53cd29b", "parent_id": "451b2e53cd29f", "target_type": "api", "name": "展示登录二维码", "version": 1, "sort": 3000, "method": "GET", "url": "/login/ShowQrCode?key={{TOKEN_KEY}}", "protocol": "http/1.1", "mark_id": "1", "description": "", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>", "kv": {"key": "", "value": "", "in": "header"}, "bearer": {"key": ""}, "basic": {"username": "", "password": ""}, "digest": {"username": "", "password": "", "realm": "", "nonce": "", "algorithm": "", "qop": "", "nc": "", "cnonce": "", "opaque": ""}, "hawk": {"authId": "", "authKey": "", "algorithm": "", "user": "", "nonce": "", "extraData": "", "default": "", "delegation": "", "timestamp": "", "includePayloadHash": false}, "awsv4": {"accessKey": "", "secretKey": "", "region": "", "service": "", "sessionToken": "", "addAuthDataToQuery": false}, "ntlm": {"username": "", "password": "", "domain": "", "workstation": "", "disableRetryRequest": false}, "edgegrid": {"accessToken": "", "clientToken": "", "clientSecret": "", "nonce": "", "timestamp": "", "baseURi": "", "headersToSign": ""}, "oauth1": {"consumerKey": "", "consumerSecret": "", "signatureMethod": "HMAC-SHA1", "addEmptyParamsToSign": true, "includeBodyHash": true, "addParamsToHeader": false, "disableHeaderEncoding": false, "realm": "", "version": "1.0", "nonce": "", "timestamp": "", "verifier": "", "callback": "", "tokenSecret": "", "token": ""}, "jwt": {"addTokenTo": "header", "algorithm": "HS256", "secret": "", "isSecretBase64Encoded": false, "payload": "", "headerPrefix": "Bearer", "queryParamKey": "token", "header": ""}, "asap": {"alg": "HS256", "iss": "", "aud": "", "kid": "", "privateKey": "", "sub": "", "claims": "", "exp": ""}}, "body": {"mode": "json", "parameter": [], "raw": "", "raw_parameter": [], "raw_schema": {"type": "object", "properties": {}}, "binary": {}}, "pre_tasks": [], "post_tasks": [], "header": {"parameter": []}, "query": {"query_add_equal": 1, "parameter": [{"param_id": "451b2e5bcd2e6", "description": "", "field_type": "String", "is_checked": 1, "key": "key", "not_null": -1, "value": "{{TOKEN_KEY}}"}]}, "cookie": {"parameter": []}, "restful": {"parameter": []}}, "response": {"example": [{"example_id": "451b2e5bcd2e7", "raw": "", "raw_parameter": [], "expect": {"code": "200", "content_type": "json", "is_default": 1, "mock": "", "name": "成功", "schema": {"type": "object", "properties": {}}, "verify_type": "schema"}}], "is_check_result": 1}, "mock_server_enable": -1, "attribute_info": {}, "tags": []}, {"target_id": "451b2e53cd2b2", "project_id": "451b2e53cd29b", "parent_id": "451b2e53cd2a0", "target_type": "api", "name": "发送文本消息", "version": 1, "sort": 3000, "method": "POST", "url": "/message/SendTextMessage?key={{TOKEN_KEY}}", "protocol": "http/1.1", "mark_id": "1", "description": "", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>", "kv": {"key": "", "value": "", "in": "header"}, "bearer": {"key": ""}, "basic": {"username": "", "password": ""}, "digest": {"username": "", "password": "", "realm": "", "nonce": "", "algorithm": "", "qop": "", "nc": "", "cnonce": "", "opaque": ""}, "hawk": {"authId": "", "authKey": "", "algorithm": "", "user": "", "nonce": "", "extraData": "", "default": "", "delegation": "", "timestamp": "", "includePayloadHash": false}, "awsv4": {"accessKey": "", "secretKey": "", "region": "", "service": "", "sessionToken": "", "addAuthDataToQuery": false}, "ntlm": {"username": "", "password": "", "domain": "", "workstation": "", "disableRetryRequest": false}, "edgegrid": {"accessToken": "", "clientToken": "", "clientSecret": "", "nonce": "", "timestamp": "", "baseURi": "", "headersToSign": ""}, "oauth1": {"consumerKey": "", "consumerSecret": "", "signatureMethod": "HMAC-SHA1", "addEmptyParamsToSign": true, "includeBodyHash": true, "addParamsToHeader": false, "disableHeaderEncoding": false, "realm": "", "version": "1.0", "nonce": "", "timestamp": "", "verifier": "", "callback": "", "tokenSecret": "", "token": ""}, "jwt": {"addTokenTo": "header", "algorithm": "HS256", "secret": "", "isSecretBase64Encoded": false, "payload": "", "headerPrefix": "Bearer", "queryParamKey": "token", "header": ""}, "asap": {"alg": "HS256", "iss": "", "aud": "", "kid": "", "privateKey": "", "sub": "", "claims": "", "exp": ""}}, "body": {"mode": "json", "parameter": [], "raw": "{\n\t\"MsgItem\": [\n\t\t{\n\t\t\t\"AtWxIDList\": [],\n\t\t\t\"ImageContent\": \"\",\n\t\t\t\"MsgType\": 1,\n\t\t\t\"TextContent\": \"你好啊\",\n\t\t\t\"ToUserName\": \"{{Wxid1}}\"\n\t\t}\n\t]\n}", "raw_parameter": [], "raw_schema": {"type": "object", "properties": {}}, "binary": {}}, "pre_tasks": [], "post_tasks": [], "header": {"parameter": []}, "query": {"query_add_equal": 1, "parameter": [{"param_id": "451b2e5bcd2ed", "description": "", "field_type": "String", "is_checked": 1, "key": "key", "not_null": -1, "value": "{{TOKEN_KEY}}"}]}, "cookie": {"parameter": []}, "restful": {"parameter": []}}, "response": {"example": [{"example_id": "451b2e5bcd2ee", "raw": "", "raw_parameter": [], "expect": {"code": "200", "content_type": "json", "is_default": 1, "mock": "", "name": "成功", "schema": {"type": "object", "properties": {}}, "verify_type": "schema"}}], "is_check_result": 1}, "mock_server_enable": -1, "attribute_info": {}, "tags": []}, {"target_id": "451b2e53cd2b3", "project_id": "451b2e53cd29b", "parent_id": "451b2e53cd2a2", "target_type": "api", "name": "获取手机通讯录好友", "version": 1, "sort": 4000, "method": "GET", "url": "/friend/GetMFriend?key={{TOKEN_KEY}}", "protocol": "http/1.1", "mark_id": "1", "description": "", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>", "kv": {"key": "", "value": "", "in": "header"}, "bearer": {"key": ""}, "basic": {"username": "", "password": ""}, "digest": {"username": "", "password": "", "realm": "", "nonce": "", "algorithm": "", "qop": "", "nc": "", "cnonce": "", "opaque": ""}, "hawk": {"authId": "", "authKey": "", "algorithm": "", "user": "", "nonce": "", "extraData": "", "default": "", "delegation": "", "timestamp": "", "includePayloadHash": false}, "awsv4": {"accessKey": "", "secretKey": "", "region": "", "service": "", "sessionToken": "", "addAuthDataToQuery": false}, "ntlm": {"username": "", "password": "", "domain": "", "workstation": "", "disableRetryRequest": false}, "edgegrid": {"accessToken": "", "clientToken": "", "clientSecret": "", "nonce": "", "timestamp": "", "baseURi": "", "headersToSign": ""}, "oauth1": {"consumerKey": "", "consumerSecret": "", "signatureMethod": "HMAC-SHA1", "addEmptyParamsToSign": true, "includeBodyHash": true, "addParamsToHeader": false, "disableHeaderEncoding": false, "realm": "", "version": "1.0", "nonce": "", "timestamp": "", "verifier": "", "callback": "", "tokenSecret": "", "token": ""}, "jwt": {"addTokenTo": "header", "algorithm": "HS256", "secret": "", "isSecretBase64Encoded": false, "payload": "", "headerPrefix": "Bearer", "queryParamKey": "token", "header": ""}, "asap": {"alg": "HS256", "iss": "", "aud": "", "kid": "", "privateKey": "", "sub": "", "claims": "", "exp": ""}}, "body": {"mode": "json", "parameter": [], "raw": "", "raw_parameter": [], "raw_schema": {"type": "object", "properties": {}}, "binary": {}}, "pre_tasks": [], "post_tasks": [], "header": {"parameter": []}, "query": {"query_add_equal": 1, "parameter": [{"param_id": "451b2e5bcd2e4", "description": "", "field_type": "String", "is_checked": 1, "key": "key", "not_null": -1, "value": "{{TOKEN_KEY}}"}]}, "cookie": {"parameter": []}, "restful": {"parameter": []}}, "response": {"example": [{"example_id": "451b2e5bcd2e5", "raw": "", "raw_parameter": [], "expect": {"code": "200", "content_type": "json", "is_default": 1, "mock": "", "name": "成功", "schema": {"type": "object", "properties": {}}, "verify_type": "schema"}}], "is_check_result": 1}, "mock_server_enable": -1, "attribute_info": {}, "tags": []}, {"target_id": "451b2e53cd2b4", "project_id": "451b2e53cd29b", "parent_id": "451b2e53cd29f", "target_type": "api", "name": "获取登录二维码", "version": 1, "sort": 4000, "method": "POST", "url": "/login/GetLoginQrCodeNew?key={{TOKEN_KEY}}", "protocol": "http/1.1", "mark_id": "1", "description": "", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>", "kv": {"key": "", "value": "", "in": "header"}, "bearer": {"key": ""}, "basic": {"username": "", "password": ""}, "digest": {"username": "", "password": "", "realm": "", "nonce": "", "algorithm": "", "qop": "", "nc": "", "cnonce": "", "opaque": ""}, "hawk": {"authId": "", "authKey": "", "algorithm": "", "user": "", "nonce": "", "extraData": "", "default": "", "delegation": "", "timestamp": "", "includePayloadHash": false}, "awsv4": {"accessKey": "", "secretKey": "", "region": "", "service": "", "sessionToken": "", "addAuthDataToQuery": false}, "ntlm": {"username": "", "password": "", "domain": "", "workstation": "", "disableRetryRequest": false}, "edgegrid": {"accessToken": "", "clientToken": "", "clientSecret": "", "nonce": "", "timestamp": "", "baseURi": "", "headersToSign": ""}, "oauth1": {"consumerKey": "", "consumerSecret": "", "signatureMethod": "HMAC-SHA1", "addEmptyParamsToSign": true, "includeBodyHash": true, "addParamsToHeader": false, "disableHeaderEncoding": false, "realm": "", "version": "1.0", "nonce": "", "timestamp": "", "verifier": "", "callback": "", "tokenSecret": "", "token": ""}, "jwt": {"addTokenTo": "header", "algorithm": "HS256", "secret": "", "isSecretBase64Encoded": false, "payload": "", "headerPrefix": "Bearer", "queryParamKey": "token", "header": ""}, "asap": {"alg": "HS256", "iss": "", "aud": "", "kid": "", "privateKey": "", "sub": "", "claims": "", "exp": ""}}, "body": {"mode": "json", "parameter": [], "raw": "{\n\t\"Proxy\": \"{{SOCKS5}}\"\n}", "raw_parameter": [], "raw_schema": {"type": "object", "properties": {}}, "binary": {}}, "pre_tasks": [], "post_tasks": [{"type": "customScript", "enabled": 1, "data": "let res = pm.response.json();\nif (res[\"Code\"] != 200 || !res[\"Data\"]) {\n    return;\n}\nlet qrCodeUrl = res[\"Data\"][\"QrCodeUrl\"];\nconsole.log(qrCodeUrl);\n\nconst template = `<html><img height=\"200px\" width=\"200px\" src=\"{{imgTemplate}}\" /></html>`;\n\npm.visualizer.set(template, {\n    imgTemplate: qrCodeUrl,\n})\n", "name": "自定义脚本", "id": "451b2e5bcd2ea"}], "header": {"parameter": []}, "query": {"query_add_equal": 1, "parameter": [{"param_id": "451b2e5bcd2eb", "description": "", "field_type": "String", "is_checked": 1, "key": "key", "not_null": -1, "value": "{{TOKEN_KEY}}"}]}, "cookie": {"parameter": []}, "restful": {"parameter": []}}, "response": {"example": [{"example_id": "451b2e5bcd2ec", "raw": "", "raw_parameter": [], "expect": {"code": "200", "content_type": "json", "is_default": 1, "mock": "", "name": "成功", "schema": {"type": "object", "properties": {}}, "verify_type": "schema"}}], "is_check_result": 1}, "mock_server_enable": -1, "attribute_info": {}, "tags": []}, {"target_id": "451b2e53cd2b5", "project_id": "451b2e53cd29b", "parent_id": "451b2e53cd2a0", "target_type": "api", "name": "发送文本消息_群AT", "version": 1, "sort": 4000, "method": "POST", "url": "/message/SendTextMessage?key={{TOKEN_KEY}}", "protocol": "http/1.1", "mark_id": "1", "description": "", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>", "kv": {"key": "", "value": "", "in": "header"}, "bearer": {"key": ""}, "basic": {"username": "", "password": ""}, "digest": {"username": "", "password": "", "realm": "", "nonce": "", "algorithm": "", "qop": "", "nc": "", "cnonce": "", "opaque": ""}, "hawk": {"authId": "", "authKey": "", "algorithm": "", "user": "", "nonce": "", "extraData": "", "default": "", "delegation": "", "timestamp": "", "includePayloadHash": false}, "awsv4": {"accessKey": "", "secretKey": "", "region": "", "service": "", "sessionToken": "", "addAuthDataToQuery": false}, "ntlm": {"username": "", "password": "", "domain": "", "workstation": "", "disableRetryRequest": false}, "edgegrid": {"accessToken": "", "clientToken": "", "clientSecret": "", "nonce": "", "timestamp": "", "baseURi": "", "headersToSign": ""}, "oauth1": {"consumerKey": "", "consumerSecret": "", "signatureMethod": "HMAC-SHA1", "addEmptyParamsToSign": true, "includeBodyHash": true, "addParamsToHeader": false, "disableHeaderEncoding": false, "realm": "", "version": "1.0", "nonce": "", "timestamp": "", "verifier": "", "callback": "", "tokenSecret": "", "token": ""}, "jwt": {"addTokenTo": "header", "algorithm": "HS256", "secret": "", "isSecretBase64Encoded": false, "payload": "", "headerPrefix": "Bearer", "queryParamKey": "token", "header": ""}, "asap": {"alg": "HS256", "iss": "", "aud": "", "kid": "", "privateKey": "", "sub": "", "claims": "", "exp": ""}}, "body": {"mode": "json", "parameter": [], "raw": "{\n\t\"MsgItem\": [\n\t\t{\n\t\t\t\"AtWxIDList\": [\n\t\t\t\t\"{{Wxid1}}\"\n\t\t\t],\n\t\t\t\"ImageContent\": \"\",\n\t\t\t\"MsgType\": 1,\n\t\t\t\"TextContent\": \"@XXX 你好啊\",\n\t\t\t\"ToUserName\": \"{{QID}}\"\n\t\t}\n\t]\n}", "raw_parameter": [], "raw_schema": {"type": "object", "properties": {}}, "binary": {}}, "pre_tasks": [], "post_tasks": [], "header": {"parameter": []}, "query": {"query_add_equal": 1, "parameter": [{"param_id": "451b2e5fcd301", "description": "", "field_type": "String", "is_checked": 1, "key": "key", "not_null": -1, "value": "{{TOKEN_KEY}}"}]}, "cookie": {"parameter": []}, "restful": {"parameter": []}}, "response": {"example": [{"example_id": "451b2e5fcd302", "raw": "", "raw_parameter": [], "expect": {"code": "200", "content_type": "json", "is_default": 1, "mock": "", "name": "成功", "schema": {"type": "object", "properties": {}}, "verify_type": "schema"}}], "is_check_result": 1}, "mock_server_enable": -1, "attribute_info": {}, "tags": []}, {"target_id": "451b2e53cd2b6", "project_id": "451b2e53cd29b", "parent_id": "451b2e53cd2a2", "target_type": "api", "name": "获取全部联系人", "version": 1, "sort": 5000, "method": "POST", "url": "/friend/GetContactList?key={{TOKEN_KEY}}", "protocol": "http/1.1", "mark_id": "1", "description": "", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>", "kv": {"key": "", "value": "", "in": "header"}, "bearer": {"key": ""}, "basic": {"username": "", "password": ""}, "digest": {"username": "", "password": "", "realm": "", "nonce": "", "algorithm": "", "qop": "", "nc": "", "cnonce": "", "opaque": ""}, "hawk": {"authId": "", "authKey": "", "algorithm": "", "user": "", "nonce": "", "extraData": "", "default": "", "delegation": "", "timestamp": "", "includePayloadHash": false}, "awsv4": {"accessKey": "", "secretKey": "", "region": "", "service": "", "sessionToken": "", "addAuthDataToQuery": false}, "ntlm": {"username": "", "password": "", "domain": "", "workstation": "", "disableRetryRequest": false}, "edgegrid": {"accessToken": "", "clientToken": "", "clientSecret": "", "nonce": "", "timestamp": "", "baseURi": "", "headersToSign": ""}, "oauth1": {"consumerKey": "", "consumerSecret": "", "signatureMethod": "HMAC-SHA1", "addEmptyParamsToSign": true, "includeBodyHash": true, "addParamsToHeader": false, "disableHeaderEncoding": false, "realm": "", "version": "1.0", "nonce": "", "timestamp": "", "verifier": "", "callback": "", "tokenSecret": "", "token": ""}, "jwt": {"addTokenTo": "header", "algorithm": "HS256", "secret": "", "isSecretBase64Encoded": false, "payload": "", "headerPrefix": "Bearer", "queryParamKey": "token", "header": ""}, "asap": {"alg": "HS256", "iss": "", "aud": "", "kid": "", "privateKey": "", "sub": "", "claims": "", "exp": ""}}, "body": {"mode": "json", "parameter": [], "raw": "{\n\t\"CurrentChatRoomContactSeq\": 0,\n\t\"CurrentWxcontactSeq\": 0\n}", "raw_parameter": [], "raw_schema": {"type": "object", "properties": {}}, "binary": {}}, "pre_tasks": [], "post_tasks": [], "header": {"parameter": []}, "query": {"query_add_equal": 1, "parameter": [{"param_id": "451b2e5bcd2e8", "description": "", "field_type": "String", "is_checked": 1, "key": "key", "not_null": -1, "value": "{{TOKEN_KEY}}"}]}, "cookie": {"parameter": []}, "restful": {"parameter": []}}, "response": {"example": [{"example_id": "451b2e5bcd2e9", "raw": "", "raw_parameter": [], "expect": {"code": "200", "content_type": "json", "is_default": 1, "mock": "", "name": "成功", "schema": {"type": "object", "properties": {}}, "verify_type": "schema"}}], "is_check_result": 1}, "mock_server_enable": -1, "attribute_info": {}, "tags": []}, {"target_id": "451b2e53cd2b7", "project_id": "451b2e53cd29b", "parent_id": "451b2e53cd29f", "target_type": "api", "name": "检测登录二维码", "version": 1, "sort": 5000, "method": "GET", "url": "/login/CheckLoginStatus?key={{TOKEN_KEY}}", "protocol": "http/1.1", "mark_id": "1", "description": "", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>", "kv": {"key": "", "value": "", "in": "header"}, "bearer": {"key": ""}, "basic": {"username": "", "password": ""}, "digest": {"username": "", "password": "", "realm": "", "nonce": "", "algorithm": "", "qop": "", "nc": "", "cnonce": "", "opaque": ""}, "hawk": {"authId": "", "authKey": "", "algorithm": "", "user": "", "nonce": "", "extraData": "", "default": "", "delegation": "", "timestamp": "", "includePayloadHash": false}, "awsv4": {"accessKey": "", "secretKey": "", "region": "", "service": "", "sessionToken": "", "addAuthDataToQuery": false}, "ntlm": {"username": "", "password": "", "domain": "", "workstation": "", "disableRetryRequest": false}, "edgegrid": {"accessToken": "", "clientToken": "", "clientSecret": "", "nonce": "", "timestamp": "", "baseURi": "", "headersToSign": ""}, "oauth1": {"consumerKey": "", "consumerSecret": "", "signatureMethod": "HMAC-SHA1", "addEmptyParamsToSign": true, "includeBodyHash": true, "addParamsToHeader": false, "disableHeaderEncoding": false, "realm": "", "version": "1.0", "nonce": "", "timestamp": "", "verifier": "", "callback": "", "tokenSecret": "", "token": ""}, "jwt": {"addTokenTo": "header", "algorithm": "HS256", "secret": "", "isSecretBase64Encoded": false, "payload": "", "headerPrefix": "Bearer", "queryParamKey": "token", "header": ""}, "asap": {"alg": "HS256", "iss": "", "aud": "", "kid": "", "privateKey": "", "sub": "", "claims": "", "exp": ""}}, "body": {"mode": "json", "parameter": [], "raw": "", "raw_parameter": [], "raw_schema": {"type": "object", "properties": {}}, "binary": {}}, "pre_tasks": [], "post_tasks": [{"type": "customScript", "enabled": 1, "data": "let res = pm.response.json();\nif (res[\"Code\"] != 200 || !res[\"Data\"]) {\n    return;\n}\nlet wxid = res[\"Data\"][\"wxid\"];\nif (!wxid) {\n    return;\n}\nconsole.log(wxid);\npm.environment.set(\"Wxid\", wxid);\n", "name": "自定义脚本", "id": "451b2e5bcd2f1"}], "header": {"parameter": []}, "query": {"query_add_equal": 1, "parameter": [{"param_id": "451b2e5bcd2f2", "description": "", "field_type": "String", "is_checked": 1, "key": "key", "not_null": -1, "value": "{{TOKEN_KEY}}"}]}, "cookie": {"parameter": []}, "restful": {"parameter": []}}, "response": {"example": [{"example_id": "451b2e5bcd2f3", "raw": "", "raw_parameter": [], "expect": {"code": "200", "content_type": "json", "is_default": 1, "mock": "", "name": "成功", "schema": {"type": "object", "properties": {}}, "verify_type": "schema"}}], "is_check_result": 1}, "mock_server_enable": -1, "attribute_info": {}, "tags": []}, {"target_id": "451b2e57cd2b8", "project_id": "451b2e53cd29b", "parent_id": "451b2e53cd2a0", "target_type": "api", "name": "群发文本消息", "version": 1, "sort": 5000, "method": "POST", "url": "/message/GroupMassMsgText?key={{TOKEN_KEY}}", "protocol": "http/1.1", "mark_id": "1", "description": "", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>", "kv": {"key": "", "value": "", "in": "header"}, "bearer": {"key": ""}, "basic": {"username": "", "password": ""}, "digest": {"username": "", "password": "", "realm": "", "nonce": "", "algorithm": "", "qop": "", "nc": "", "cnonce": "", "opaque": ""}, "hawk": {"authId": "", "authKey": "", "algorithm": "", "user": "", "nonce": "", "extraData": "", "default": "", "delegation": "", "timestamp": "", "includePayloadHash": false}, "awsv4": {"accessKey": "", "secretKey": "", "region": "", "service": "", "sessionToken": "", "addAuthDataToQuery": false}, "ntlm": {"username": "", "password": "", "domain": "", "workstation": "", "disableRetryRequest": false}, "edgegrid": {"accessToken": "", "clientToken": "", "clientSecret": "", "nonce": "", "timestamp": "", "baseURi": "", "headersToSign": ""}, "oauth1": {"consumerKey": "", "consumerSecret": "", "signatureMethod": "HMAC-SHA1", "addEmptyParamsToSign": true, "includeBodyHash": true, "addParamsToHeader": false, "disableHeaderEncoding": false, "realm": "", "version": "1.0", "nonce": "", "timestamp": "", "verifier": "", "callback": "", "tokenSecret": "", "token": ""}, "jwt": {"addTokenTo": "header", "algorithm": "HS256", "secret": "", "isSecretBase64Encoded": false, "payload": "", "headerPrefix": "Bearer", "queryParamKey": "token", "header": ""}, "asap": {"alg": "HS256", "iss": "", "aud": "", "kid": "", "privateKey": "", "sub": "", "claims": "", "exp": ""}}, "body": {"mode": "json", "parameter": [], "raw": "{\n\t\"Content\": \"你好啊\",\n\t\"ToUserName\": [\n\t\t\"{{Wxid1}}\"\n\t]\n}", "raw_parameter": [], "raw_schema": {"type": "object", "properties": {}}, "binary": {}}, "pre_tasks": [], "post_tasks": [], "header": {"parameter": []}, "query": {"query_add_equal": 1, "parameter": [{"param_id": "451b2e5fcd303", "description": "", "field_type": "String", "is_checked": 1, "key": "key", "not_null": -1, "value": "{{TOKEN_KEY}}"}]}, "cookie": {"parameter": []}, "restful": {"parameter": []}}, "response": {"example": [{"example_id": "451b2e5fcd304", "raw": "", "raw_parameter": [], "expect": {"code": "200", "content_type": "json", "is_default": 1, "mock": "", "name": "成功", "schema": {"type": "object", "properties": {}}, "verify_type": "schema"}}], "is_check_result": 1}, "mock_server_enable": -1, "attribute_info": {}, "tags": []}, {"target_id": "451b2e57cd2b9", "project_id": "451b2e53cd29b", "parent_id": "451b2e53cd29f", "target_type": "api", "name": "获取初始化状态", "version": 1, "sort": 6000, "method": "GET", "url": "/login/GetInItStatus?key={{TOKEN_KEY}}", "protocol": "http/1.1", "mark_id": "1", "description": "", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>", "kv": {"key": "", "value": "", "in": "header"}, "bearer": {"key": ""}, "basic": {"username": "", "password": ""}, "digest": {"username": "", "password": "", "realm": "", "nonce": "", "algorithm": "", "qop": "", "nc": "", "cnonce": "", "opaque": ""}, "hawk": {"authId": "", "authKey": "", "algorithm": "", "user": "", "nonce": "", "extraData": "", "default": "", "delegation": "", "timestamp": "", "includePayloadHash": false}, "awsv4": {"accessKey": "", "secretKey": "", "region": "", "service": "", "sessionToken": "", "addAuthDataToQuery": false}, "ntlm": {"username": "", "password": "", "domain": "", "workstation": "", "disableRetryRequest": false}, "edgegrid": {"accessToken": "", "clientToken": "", "clientSecret": "", "nonce": "", "timestamp": "", "baseURi": "", "headersToSign": ""}, "oauth1": {"consumerKey": "", "consumerSecret": "", "signatureMethod": "HMAC-SHA1", "addEmptyParamsToSign": true, "includeBodyHash": true, "addParamsToHeader": false, "disableHeaderEncoding": false, "realm": "", "version": "1.0", "nonce": "", "timestamp": "", "verifier": "", "callback": "", "tokenSecret": "", "token": ""}, "jwt": {"addTokenTo": "header", "algorithm": "HS256", "secret": "", "isSecretBase64Encoded": false, "payload": "", "headerPrefix": "Bearer", "queryParamKey": "token", "header": ""}, "asap": {"alg": "HS256", "iss": "", "aud": "", "kid": "", "privateKey": "", "sub": "", "claims": "", "exp": ""}}, "body": {"mode": "json", "parameter": [], "raw": "", "raw_parameter": [], "raw_schema": {"type": "object", "properties": {}}, "binary": {}}, "pre_tasks": [], "post_tasks": [], "header": {"parameter": []}, "query": {"query_add_equal": 1, "parameter": [{"param_id": "451b2e5bcd2f4", "description": "", "field_type": "String", "is_checked": 1, "key": "key", "not_null": -1, "value": "{{TOKEN_KEY}}"}]}, "cookie": {"parameter": []}, "restful": {"parameter": []}}, "response": {"example": [{"example_id": "451b2e5bcd2f5", "raw": "", "raw_parameter": [], "expect": {"code": "200", "content_type": "json", "is_default": 1, "mock": "", "name": "成功", "schema": {"type": "object", "properties": {}}, "verify_type": "schema"}}], "is_check_result": 1}, "mock_server_enable": -1, "attribute_info": {}, "tags": []}, {"target_id": "451b2e57cd2ba", "project_id": "451b2e53cd29b", "parent_id": "451b2e53cd2a0", "target_type": "api", "name": "撤销消息", "version": 1, "sort": 6000, "method": "POST", "url": "/message/RevokeMsg?key={{TOKEN_KEY}}", "protocol": "http/1.1", "mark_id": "1", "description": "", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>", "kv": {"key": "", "value": "", "in": "header"}, "bearer": {"key": ""}, "basic": {"username": "", "password": ""}, "digest": {"username": "", "password": "", "realm": "", "nonce": "", "algorithm": "", "qop": "", "nc": "", "cnonce": "", "opaque": ""}, "hawk": {"authId": "", "authKey": "", "algorithm": "", "user": "", "nonce": "", "extraData": "", "default": "", "delegation": "", "timestamp": "", "includePayloadHash": false}, "awsv4": {"accessKey": "", "secretKey": "", "region": "", "service": "", "sessionToken": "", "addAuthDataToQuery": false}, "ntlm": {"username": "", "password": "", "domain": "", "workstation": "", "disableRetryRequest": false}, "edgegrid": {"accessToken": "", "clientToken": "", "clientSecret": "", "nonce": "", "timestamp": "", "baseURi": "", "headersToSign": ""}, "oauth1": {"consumerKey": "", "consumerSecret": "", "signatureMethod": "HMAC-SHA1", "addEmptyParamsToSign": true, "includeBodyHash": true, "addParamsToHeader": false, "disableHeaderEncoding": false, "realm": "", "version": "1.0", "nonce": "", "timestamp": "", "verifier": "", "callback": "", "tokenSecret": "", "token": ""}, "jwt": {"addTokenTo": "header", "algorithm": "HS256", "secret": "", "isSecretBase64Encoded": false, "payload": "", "headerPrefix": "Bearer", "queryParamKey": "token", "header": ""}, "asap": {"alg": "HS256", "iss": "", "aud": "", "kid": "", "privateKey": "", "sub": "", "claims": "", "exp": ""}}, "body": {"mode": "json", "parameter": [], "raw": "{\n\t\"ClientMsgId\": 0,\n\t\"NewMsgId\": \"\",\n\t\"ToUserName\": \"\"\n}", "raw_parameter": [], "raw_schema": {"type": "object", "properties": {}}, "binary": {}}, "pre_tasks": [], "post_tasks": [], "header": {"parameter": []}, "query": {"query_add_equal": 1, "parameter": [{"param_id": "451b2e5fcd307", "description": "", "field_type": "String", "is_checked": 1, "key": "key", "not_null": -1, "value": "{{TOKEN_KEY}}"}]}, "cookie": {"parameter": []}, "restful": {"parameter": []}}, "response": {"example": [{"example_id": "451b2e5fcd308", "raw": "", "raw_parameter": [], "expect": {"code": "200", "content_type": "json", "is_default": 1, "mock": "", "name": "成功", "schema": {"type": "object", "properties": {}}, "verify_type": "schema"}}], "is_check_result": 1}, "mock_server_enable": -1, "attribute_info": {}, "tags": []}, {"target_id": "451b2e57cd2bb", "project_id": "451b2e53cd29b", "parent_id": "451b2e53cd2a2", "target_type": "api", "name": "获取联系人详情", "version": 1, "sort": 6000, "method": "POST", "url": "/friend/GetContactDetailsList?key={{TOKEN_KEY}}", "protocol": "http/1.1", "mark_id": "1", "description": "", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>", "kv": {"key": "", "value": "", "in": "header"}, "bearer": {"key": ""}, "basic": {"username": "", "password": ""}, "digest": {"username": "", "password": "", "realm": "", "nonce": "", "algorithm": "", "qop": "", "nc": "", "cnonce": "", "opaque": ""}, "hawk": {"authId": "", "authKey": "", "algorithm": "", "user": "", "nonce": "", "extraData": "", "default": "", "delegation": "", "timestamp": "", "includePayloadHash": false}, "awsv4": {"accessKey": "", "secretKey": "", "region": "", "service": "", "sessionToken": "", "addAuthDataToQuery": false}, "ntlm": {"username": "", "password": "", "domain": "", "workstation": "", "disableRetryRequest": false}, "edgegrid": {"accessToken": "", "clientToken": "", "clientSecret": "", "nonce": "", "timestamp": "", "baseURi": "", "headersToSign": ""}, "oauth1": {"consumerKey": "", "consumerSecret": "", "signatureMethod": "HMAC-SHA1", "addEmptyParamsToSign": true, "includeBodyHash": true, "addParamsToHeader": false, "disableHeaderEncoding": false, "realm": "", "version": "1.0", "nonce": "", "timestamp": "", "verifier": "", "callback": "", "tokenSecret": "", "token": ""}, "jwt": {"addTokenTo": "header", "algorithm": "HS256", "secret": "", "isSecretBase64Encoded": false, "payload": "", "headerPrefix": "Bearer", "queryParamKey": "token", "header": ""}, "asap": {"alg": "HS256", "iss": "", "aud": "", "kid": "", "privateKey": "", "sub": "", "claims": "", "exp": ""}}, "body": {"mode": "json", "parameter": [], "raw": "{\n\t\"RoomWxIDList\": [\n\t\t\"\"\n\t],\n\t\"UserNames\": [\n\t\t\"\"\n\t]\n}", "raw_parameter": [], "raw_schema": {"type": "object", "properties": {}}, "binary": {}}, "pre_tasks": [], "post_tasks": [], "header": {"parameter": []}, "query": {"query_add_equal": 1, "parameter": [{"param_id": "451b2e5fcd30b", "description": "", "field_type": "String", "is_checked": 1, "key": "key", "not_null": -1, "value": "{{TOKEN_KEY}}"}]}, "cookie": {"parameter": []}, "restful": {"parameter": []}}, "response": {"example": [{"example_id": "451b2e5fcd30c", "raw": "", "raw_parameter": [], "expect": {"code": "200", "content_type": "json", "is_default": 1, "mock": "", "name": "成功", "schema": {"type": "object", "properties": {}}, "verify_type": "schema"}}], "is_check_result": 1}, "mock_server_enable": -1, "attribute_info": {}, "tags": []}, {"target_id": "451b2e57cd2bc", "project_id": "451b2e53cd29b", "parent_id": "451b2e53cd29f", "target_type": "api", "name": "获取在线状态", "version": 1, "sort": 7000, "method": "GET", "url": "/login/GetLoginStatus?key={{TOKEN_KEY}}", "protocol": "http/1.1", "mark_id": "1", "description": "", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>", "kv": {"key": "", "value": "", "in": "header"}, "bearer": {"key": ""}, "basic": {"username": "", "password": ""}, "digest": {"username": "", "password": "", "realm": "", "nonce": "", "algorithm": "", "qop": "", "nc": "", "cnonce": "", "opaque": ""}, "hawk": {"authId": "", "authKey": "", "algorithm": "", "user": "", "nonce": "", "extraData": "", "default": "", "delegation": "", "timestamp": "", "includePayloadHash": false}, "awsv4": {"accessKey": "", "secretKey": "", "region": "", "service": "", "sessionToken": "", "addAuthDataToQuery": false}, "ntlm": {"username": "", "password": "", "domain": "", "workstation": "", "disableRetryRequest": false}, "edgegrid": {"accessToken": "", "clientToken": "", "clientSecret": "", "nonce": "", "timestamp": "", "baseURi": "", "headersToSign": ""}, "oauth1": {"consumerKey": "", "consumerSecret": "", "signatureMethod": "HMAC-SHA1", "addEmptyParamsToSign": true, "includeBodyHash": true, "addParamsToHeader": false, "disableHeaderEncoding": false, "realm": "", "version": "1.0", "nonce": "", "timestamp": "", "verifier": "", "callback": "", "tokenSecret": "", "token": ""}, "jwt": {"addTokenTo": "header", "algorithm": "HS256", "secret": "", "isSecretBase64Encoded": false, "payload": "", "headerPrefix": "Bearer", "queryParamKey": "token", "header": ""}, "asap": {"alg": "HS256", "iss": "", "aud": "", "kid": "", "privateKey": "", "sub": "", "claims": "", "exp": ""}}, "body": {"mode": "json", "parameter": [], "raw": "", "raw_parameter": [], "raw_schema": {"type": "object", "properties": {}}, "binary": {}}, "pre_tasks": [], "post_tasks": [], "header": {"parameter": []}, "query": {"query_add_equal": 1, "parameter": [{"param_id": "451b2e5bcd2f6", "description": "", "field_type": "String", "is_checked": 1, "key": "key", "not_null": -1, "value": "{{TOKEN_KEY}}"}]}, "cookie": {"parameter": []}, "restful": {"parameter": []}}, "response": {"example": [{"example_id": "451b2e5fcd2f7", "raw": "", "raw_parameter": [], "expect": {"code": "200", "content_type": "json", "is_default": 1, "mock": "", "name": "成功", "schema": {"type": "object", "properties": {}}, "verify_type": "schema"}}], "is_check_result": 1}, "mock_server_enable": -1, "attribute_info": {}, "tags": []}, {"target_id": "451b2e57cd2bd", "project_id": "451b2e53cd29b", "parent_id": "451b2e53cd2a2", "target_type": "api", "name": "获取好友关系", "version": 1, "sort": 7000, "method": "POST", "url": "/friend/GetFriendRelation?key={{TOKEN_KEY}}", "protocol": "http/1.1", "mark_id": "1", "description": "", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>", "kv": {"key": "", "value": "", "in": "header"}, "bearer": {"key": ""}, "basic": {"username": "", "password": ""}, "digest": {"username": "", "password": "", "realm": "", "nonce": "", "algorithm": "", "qop": "", "nc": "", "cnonce": "", "opaque": ""}, "hawk": {"authId": "", "authKey": "", "algorithm": "", "user": "", "nonce": "", "extraData": "", "default": "", "delegation": "", "timestamp": "", "includePayloadHash": false}, "awsv4": {"accessKey": "", "secretKey": "", "region": "", "service": "", "sessionToken": "", "addAuthDataToQuery": false}, "ntlm": {"username": "", "password": "", "domain": "", "workstation": "", "disableRetryRequest": false}, "edgegrid": {"accessToken": "", "clientToken": "", "clientSecret": "", "nonce": "", "timestamp": "", "baseURi": "", "headersToSign": ""}, "oauth1": {"consumerKey": "", "consumerSecret": "", "signatureMethod": "HMAC-SHA1", "addEmptyParamsToSign": true, "includeBodyHash": true, "addParamsToHeader": false, "disableHeaderEncoding": false, "realm": "", "version": "1.0", "nonce": "", "timestamp": "", "verifier": "", "callback": "", "tokenSecret": "", "token": ""}, "jwt": {"addTokenTo": "header", "algorithm": "HS256", "secret": "", "isSecretBase64Encoded": false, "payload": "", "headerPrefix": "Bearer", "queryParamKey": "token", "header": ""}, "asap": {"alg": "HS256", "iss": "", "aud": "", "kid": "", "privateKey": "", "sub": "", "claims": "", "exp": ""}}, "body": {"mode": "json", "parameter": [], "raw": "{\n\t\"UserName\": \"{{Wxid1}}\"\n}", "raw_parameter": [], "raw_schema": {"type": "object", "properties": {}}, "binary": {}}, "pre_tasks": [], "post_tasks": [], "header": {"parameter": []}, "query": {"query_add_equal": 1, "parameter": [{"param_id": "451b2e5fcd30d", "description": "", "field_type": "String", "is_checked": 1, "key": "key", "not_null": -1, "value": "{{TOKEN_KEY}}"}]}, "cookie": {"parameter": []}, "restful": {"parameter": []}}, "response": {"example": [{"example_id": "451b2e5fcd30e", "raw": "", "raw_parameter": [], "expect": {"code": "200", "content_type": "json", "is_default": 1, "mock": "", "name": "成功", "schema": {"type": "object", "properties": {}}, "verify_type": "schema"}}], "is_check_result": 1}, "mock_server_enable": -1, "attribute_info": {}, "tags": []}, {"target_id": "451b2e57cd2be", "project_id": "451b2e53cd29b", "parent_id": "451b2e53cd29f", "target_type": "api", "name": "检测微信登录环境", "version": 1, "sort": 8000, "method": "GET", "url": "/login/CheckCanSetAlias?key={{TOKEN_KEY}}", "protocol": "http/1.1", "mark_id": "1", "description": "", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>", "kv": {"key": "", "value": "", "in": "header"}, "bearer": {"key": ""}, "basic": {"username": "", "password": ""}, "digest": {"username": "", "password": "", "realm": "", "nonce": "", "algorithm": "", "qop": "", "nc": "", "cnonce": "", "opaque": ""}, "hawk": {"authId": "", "authKey": "", "algorithm": "", "user": "", "nonce": "", "extraData": "", "default": "", "delegation": "", "timestamp": "", "includePayloadHash": false}, "awsv4": {"accessKey": "", "secretKey": "", "region": "", "service": "", "sessionToken": "", "addAuthDataToQuery": false}, "ntlm": {"username": "", "password": "", "domain": "", "workstation": "", "disableRetryRequest": false}, "edgegrid": {"accessToken": "", "clientToken": "", "clientSecret": "", "nonce": "", "timestamp": "", "baseURi": "", "headersToSign": ""}, "oauth1": {"consumerKey": "", "consumerSecret": "", "signatureMethod": "HMAC-SHA1", "addEmptyParamsToSign": true, "includeBodyHash": true, "addParamsToHeader": false, "disableHeaderEncoding": false, "realm": "", "version": "1.0", "nonce": "", "timestamp": "", "verifier": "", "callback": "", "tokenSecret": "", "token": ""}, "jwt": {"addTokenTo": "header", "algorithm": "HS256", "secret": "", "isSecretBase64Encoded": false, "payload": "", "headerPrefix": "Bearer", "queryParamKey": "token", "header": ""}, "asap": {"alg": "HS256", "iss": "", "aud": "", "kid": "", "privateKey": "", "sub": "", "claims": "", "exp": ""}}, "body": {"mode": "json", "parameter": [], "raw": "", "raw_parameter": [], "raw_schema": {"type": "object", "properties": {}}, "binary": {}}, "pre_tasks": [], "post_tasks": [], "header": {"parameter": []}, "query": {"query_add_equal": 1, "parameter": [{"param_id": "451b2e5fcd2f8", "description": "", "field_type": "String", "is_checked": 1, "key": "key", "not_null": -1, "value": "{{TOKEN_KEY}}"}]}, "cookie": {"parameter": []}, "restful": {"parameter": []}}, "response": {"example": [{"example_id": "451b2e5fcd2f9", "raw": "", "raw_parameter": [], "expect": {"code": "200", "content_type": "json", "is_default": 1, "mock": "", "name": "成功", "schema": {"type": "object", "properties": {}}, "verify_type": "schema"}}], "is_check_result": 1}, "mock_server_enable": -1, "attribute_info": {}, "tags": []}, {"target_id": "451b2e57cd2bf", "project_id": "451b2e53cd29b", "parent_id": "451b2e53cd2a2", "target_type": "api", "name": "删除好友", "version": 1, "sort": 8000, "method": "POST", "url": "/friend/DelContact?key={{TOKEN_KEY}}", "protocol": "http/1.1", "mark_id": "1", "description": "", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>", "kv": {"key": "", "value": "", "in": "header"}, "bearer": {"key": ""}, "basic": {"username": "", "password": ""}, "digest": {"username": "", "password": "", "realm": "", "nonce": "", "algorithm": "", "qop": "", "nc": "", "cnonce": "", "opaque": ""}, "hawk": {"authId": "", "authKey": "", "algorithm": "", "user": "", "nonce": "", "extraData": "", "default": "", "delegation": "", "timestamp": "", "includePayloadHash": false}, "awsv4": {"accessKey": "", "secretKey": "", "region": "", "service": "", "sessionToken": "", "addAuthDataToQuery": false}, "ntlm": {"username": "", "password": "", "domain": "", "workstation": "", "disableRetryRequest": false}, "edgegrid": {"accessToken": "", "clientToken": "", "clientSecret": "", "nonce": "", "timestamp": "", "baseURi": "", "headersToSign": ""}, "oauth1": {"consumerKey": "", "consumerSecret": "", "signatureMethod": "HMAC-SHA1", "addEmptyParamsToSign": true, "includeBodyHash": true, "addParamsToHeader": false, "disableHeaderEncoding": false, "realm": "", "version": "1.0", "nonce": "", "timestamp": "", "verifier": "", "callback": "", "tokenSecret": "", "token": ""}, "jwt": {"addTokenTo": "header", "algorithm": "HS256", "secret": "", "isSecretBase64Encoded": false, "payload": "", "headerPrefix": "Bearer", "queryParamKey": "token", "header": ""}, "asap": {"alg": "HS256", "iss": "", "aud": "", "kid": "", "privateKey": "", "sub": "", "claims": "", "exp": ""}}, "body": {"mode": "json", "parameter": [], "raw": "{\n\t\"DelUserName\": \"\"\n}", "raw_parameter": [], "raw_schema": {"type": "object", "properties": {}}, "binary": {}}, "pre_tasks": [], "post_tasks": [], "header": {"parameter": []}, "query": {"query_add_equal": 1, "parameter": [{"param_id": "451b2e5fcd313", "description": "", "field_type": "String", "is_checked": 1, "key": "key", "not_null": -1, "value": "{{TOKEN_KEY}}"}]}, "cookie": {"parameter": []}, "restful": {"parameter": []}}, "response": {"example": [{"example_id": "451b2e5fcd314", "raw": "", "raw_parameter": [], "expect": {"code": "200", "content_type": "json", "is_default": 1, "mock": "", "name": "成功", "schema": {"type": "object", "properties": {}}, "verify_type": "schema"}}], "is_check_result": 1}, "mock_server_enable": -1, "attribute_info": {}, "tags": []}, {"target_id": "451b2e57cd2c0", "project_id": "451b2e53cd29b", "parent_id": "451b2e53cd29f", "target_type": "api", "name": "打印链接数量", "version": 1, "sort": 9000, "method": "GET", "url": "/login/GetIWXConnect?key={{TOKEN_KEY}}", "protocol": "http/1.1", "mark_id": "1", "description": "", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>", "kv": {"key": "", "value": "", "in": "header"}, "bearer": {"key": ""}, "basic": {"username": "", "password": ""}, "digest": {"username": "", "password": "", "realm": "", "nonce": "", "algorithm": "", "qop": "", "nc": "", "cnonce": "", "opaque": ""}, "hawk": {"authId": "", "authKey": "", "algorithm": "", "user": "", "nonce": "", "extraData": "", "default": "", "delegation": "", "timestamp": "", "includePayloadHash": false}, "awsv4": {"accessKey": "", "secretKey": "", "region": "", "service": "", "sessionToken": "", "addAuthDataToQuery": false}, "ntlm": {"username": "", "password": "", "domain": "", "workstation": "", "disableRetryRequest": false}, "edgegrid": {"accessToken": "", "clientToken": "", "clientSecret": "", "nonce": "", "timestamp": "", "baseURi": "", "headersToSign": ""}, "oauth1": {"consumerKey": "", "consumerSecret": "", "signatureMethod": "HMAC-SHA1", "addEmptyParamsToSign": true, "includeBodyHash": true, "addParamsToHeader": false, "disableHeaderEncoding": false, "realm": "", "version": "1.0", "nonce": "", "timestamp": "", "verifier": "", "callback": "", "tokenSecret": "", "token": ""}, "jwt": {"addTokenTo": "header", "algorithm": "HS256", "secret": "", "isSecretBase64Encoded": false, "payload": "", "headerPrefix": "Bearer", "queryParamKey": "token", "header": ""}, "asap": {"alg": "HS256", "iss": "", "aud": "", "kid": "", "privateKey": "", "sub": "", "claims": "", "exp": ""}}, "body": {"mode": "json", "parameter": [], "raw": "", "raw_parameter": [], "raw_schema": {"type": "object", "properties": {}}, "binary": {}}, "pre_tasks": [], "post_tasks": [], "header": {"parameter": []}, "query": {"query_add_equal": 1, "parameter": [{"param_id": "451b2e5fcd2fa", "description": "", "field_type": "String", "is_checked": 1, "key": "key", "not_null": -1, "value": "{{TOKEN_KEY}}"}]}, "cookie": {"parameter": []}, "restful": {"parameter": []}}, "response": {"example": [{"example_id": "451b2e5fcd2fb", "raw": "", "raw_parameter": [], "expect": {"code": "200", "content_type": "json", "is_default": 1, "mock": "", "name": "成功", "schema": {"type": "object", "properties": {}}, "verify_type": "schema"}}], "is_check_result": 1}, "mock_server_enable": -1, "attribute_info": {}, "tags": []}, {"target_id": "451b2e57cd2c1", "project_id": "451b2e53cd29b", "parent_id": "451b2e53cd29f", "target_type": "api", "name": "获取A62数据", "version": 1, "sort": 10000, "method": "GET", "url": "/login/Get62Data?key={{TOKEN_KEY}}", "protocol": "http/1.1", "mark_id": "1", "description": "", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>", "kv": {"key": "", "value": "", "in": "header"}, "bearer": {"key": ""}, "basic": {"username": "", "password": ""}, "digest": {"username": "", "password": "", "realm": "", "nonce": "", "algorithm": "", "qop": "", "nc": "", "cnonce": "", "opaque": ""}, "hawk": {"authId": "", "authKey": "", "algorithm": "", "user": "", "nonce": "", "extraData": "", "default": "", "delegation": "", "timestamp": "", "includePayloadHash": false}, "awsv4": {"accessKey": "", "secretKey": "", "region": "", "service": "", "sessionToken": "", "addAuthDataToQuery": false}, "ntlm": {"username": "", "password": "", "domain": "", "workstation": "", "disableRetryRequest": false}, "edgegrid": {"accessToken": "", "clientToken": "", "clientSecret": "", "nonce": "", "timestamp": "", "baseURi": "", "headersToSign": ""}, "oauth1": {"consumerKey": "", "consumerSecret": "", "signatureMethod": "HMAC-SHA1", "addEmptyParamsToSign": true, "includeBodyHash": true, "addParamsToHeader": false, "disableHeaderEncoding": false, "realm": "", "version": "1.0", "nonce": "", "timestamp": "", "verifier": "", "callback": "", "tokenSecret": "", "token": ""}, "jwt": {"addTokenTo": "header", "algorithm": "HS256", "secret": "", "isSecretBase64Encoded": false, "payload": "", "headerPrefix": "Bearer", "queryParamKey": "token", "header": ""}, "asap": {"alg": "HS256", "iss": "", "aud": "", "kid": "", "privateKey": "", "sub": "", "claims": "", "exp": ""}}, "body": {"mode": "json", "parameter": [], "raw": "", "raw_parameter": [], "raw_schema": {"type": "object", "properties": {}}, "binary": {}}, "pre_tasks": [], "post_tasks": [{"type": "customScript", "enabled": 1, "data": "let res = pm.response.json();\nif (res[\"Code\"] != 200 || !res[\"Data\"]) {\n    return;\n}\nlet a62_data = res[\"Data\"];\nconsole.log(a62_data);\n\npm.environment.set(\"A62_DATA\", a62_data);\n", "name": "自定义脚本", "id": "451b2e5fcd2fc"}], "header": {"parameter": []}, "query": {"query_add_equal": 1, "parameter": [{"param_id": "451b2e5fcd2fd", "description": "", "field_type": "String", "is_checked": 1, "key": "key", "not_null": -1, "value": "{{TOKEN_KEY}}"}]}, "cookie": {"parameter": []}, "restful": {"parameter": []}}, "response": {"example": [{"example_id": "451b2e5fcd2fe", "raw": "", "raw_parameter": [], "expect": {"code": "200", "content_type": "json", "is_default": 1, "mock": "", "name": "成功", "schema": {"type": "object", "properties": {}}, "verify_type": "schema"}}], "is_check_result": 1}, "mock_server_enable": -1, "attribute_info": {}, "tags": []}, {"target_id": "451b2e57cd2c2", "project_id": "451b2e53cd29b", "parent_id": "451b2e53cd29f", "target_type": "api", "name": "A16数据登录", "version": 1, "sort": 10500, "method": "POST", "url": "login/A16Login?key={{TOKEN_KEY}}", "protocol": "http/1.1", "mark_id": "1", "description": "", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>", "kv": {"key": "", "value": "", "in": "header"}, "bearer": {"key": ""}, "basic": {"username": "", "password": ""}, "digest": {"username": "", "password": "", "realm": "", "nonce": "", "algorithm": "", "qop": "", "nc": "", "cnonce": "", "opaque": ""}, "hawk": {"authId": "", "authKey": "", "algorithm": "", "user": "", "nonce": "", "extraData": "", "default": "", "delegation": "", "timestamp": "", "includePayloadHash": false}, "awsv4": {"accessKey": "", "secretKey": "", "region": "", "service": "", "sessionToken": "", "addAuthDataToQuery": false}, "ntlm": {"username": "", "password": "", "domain": "", "workstation": "", "disableRetryRequest": false}, "edgegrid": {"accessToken": "", "clientToken": "", "clientSecret": "", "nonce": "", "timestamp": "", "baseURi": "", "headersToSign": ""}, "oauth1": {"consumerKey": "", "consumerSecret": "", "signatureMethod": "HMAC-SHA1", "addEmptyParamsToSign": true, "includeBodyHash": true, "addParamsToHeader": false, "disableHeaderEncoding": false, "realm": "", "version": "1.0", "nonce": "", "timestamp": "", "verifier": "", "callback": "", "tokenSecret": "", "token": ""}, "jwt": {"addTokenTo": "header", "algorithm": "HS256", "secret": "", "isSecretBase64Encoded": false, "payload": "", "headerPrefix": "Bearer", "queryParamKey": "token", "header": ""}, "asap": {"alg": "HS256", "iss": "", "aud": "", "kid": "", "privateKey": "", "sub": "", "claims": "", "exp": ""}}, "body": {"mode": "json", "parameter": [], "raw": "{\n\t\"DeviceId\": \"\",\n\t\"DeviceInfo\": {\n\t\t\"AndroidId\": \"\",\n\t\t\"ImeI\": \"\",\n\t\t\"Manufacturer\": \"\",\n\t\t\"Model\": \"\"\n\t},\n\t\"Password\": \"\",\n\t\"Proxy\": \"{{SOCKS5}}\",\n\t\"Ticket\": \"{{A16_DATA}}\",\n\t\"Type\": 0,\n\t\"UserName\": \"\"\n}", "raw_parameter": [], "raw_schema": {"type": "object", "properties": {}}, "binary": {}}, "pre_tasks": [], "post_tasks": [], "header": {"parameter": []}, "query": {"query_add_equal": 1, "parameter": [{"param_id": "451b2e5fcd309", "description": "", "field_type": "String", "is_checked": 1, "key": "key", "not_null": -1, "value": "{{TOKEN_KEY}}"}]}, "cookie": {"parameter": []}, "restful": {"parameter": []}}, "response": {"example": [{"example_id": "451b2e5fcd30a", "raw": "", "raw_parameter": [], "expect": {"code": "200", "content_type": "json", "is_default": 1, "mock": "", "name": "成功", "schema": {"type": "object", "properties": {}}, "verify_type": "schema"}}], "is_check_result": 1}, "mock_server_enable": -1, "attribute_info": {}, "tags": []}, {"target_id": "451b2e57cd2c3", "project_id": "451b2e53cd29b", "parent_id": "451b2e53cd29f", "target_type": "api", "name": "A62数据登录", "version": 1, "sort": 11000, "method": "POST", "url": "/login/DeviceLogin?key={{TOKEN_KEY}}", "protocol": "http/1.1", "mark_id": "1", "description": "", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>", "kv": {"key": "", "value": "", "in": "header"}, "bearer": {"key": ""}, "basic": {"username": "", "password": ""}, "digest": {"username": "", "password": "", "realm": "", "nonce": "", "algorithm": "", "qop": "", "nc": "", "cnonce": "", "opaque": ""}, "hawk": {"authId": "", "authKey": "", "algorithm": "", "user": "", "nonce": "", "extraData": "", "default": "", "delegation": "", "timestamp": "", "includePayloadHash": false}, "awsv4": {"accessKey": "", "secretKey": "", "region": "", "service": "", "sessionToken": "", "addAuthDataToQuery": false}, "ntlm": {"username": "", "password": "", "domain": "", "workstation": "", "disableRetryRequest": false}, "edgegrid": {"accessToken": "", "clientToken": "", "clientSecret": "", "nonce": "", "timestamp": "", "baseURi": "", "headersToSign": ""}, "oauth1": {"consumerKey": "", "consumerSecret": "", "signatureMethod": "HMAC-SHA1", "addEmptyParamsToSign": true, "includeBodyHash": true, "addParamsToHeader": false, "disableHeaderEncoding": false, "realm": "", "version": "1.0", "nonce": "", "timestamp": "", "verifier": "", "callback": "", "tokenSecret": "", "token": ""}, "jwt": {"addTokenTo": "header", "algorithm": "HS256", "secret": "", "isSecretBase64Encoded": false, "payload": "", "headerPrefix": "Bearer", "queryParamKey": "token", "header": ""}, "asap": {"alg": "HS256", "iss": "", "aud": "", "kid": "", "privateKey": "", "sub": "", "claims": "", "exp": ""}}, "body": {"mode": "json", "parameter": [], "raw": "{\n\t\"DeviceId\": \"\",\n\t\"DeviceInfo\": {\n\t\t\"AndroidId\": \"\",\n\t\t\"ImeI\": \"\",\n\t\t\"Manufacturer\": \"\",\n\t\t\"Model\": \"\"\n\t},\n\t\"Password\": \"\",\n\t\"Proxy\": \"{{SOCKS5}}\",\n\t\"Ticket\": \"{{A62_DATA}}\",\n\t\"Type\": 0,\n\t\"UserName\": \"\"\n}", "raw_parameter": [], "raw_schema": {"type": "object", "properties": {}}, "binary": {}}, "pre_tasks": [], "post_tasks": [], "header": {"parameter": []}, "query": {"query_add_equal": 1, "parameter": [{"param_id": "451b2e5fcd2ff", "description": "", "field_type": "String", "is_checked": 1, "key": "key", "not_null": -1, "value": "{{TOKEN_KEY}}"}]}, "cookie": {"parameter": []}, "restful": {"parameter": []}}, "response": {"example": [{"example_id": "451b2e5fcd300", "raw": "", "raw_parameter": [], "expect": {"code": "200", "content_type": "json", "is_default": 1, "mock": "", "name": "成功", "schema": {"type": "object", "properties": {}}, "verify_type": "schema"}}], "is_check_result": 1}, "mock_server_enable": -1, "attribute_info": {}, "tags": []}, {"target_id": "451b2e57cd2c4", "project_id": "451b2e53cd29b", "parent_id": "451b2e53cd29f", "target_type": "api", "name": "A62新疆号登录", "version": 1, "sort": 12000, "method": "POST", "url": "login/LoginNew?key={{TOKEN_KEY}}", "protocol": "http/1.1", "mark_id": "1", "description": "", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>", "kv": {"key": "", "value": "", "in": "header"}, "bearer": {"key": ""}, "basic": {"username": "", "password": ""}, "digest": {"username": "", "password": "", "realm": "", "nonce": "", "algorithm": "", "qop": "", "nc": "", "cnonce": "", "opaque": ""}, "hawk": {"authId": "", "authKey": "", "algorithm": "", "user": "", "nonce": "", "extraData": "", "default": "", "delegation": "", "timestamp": "", "includePayloadHash": false}, "awsv4": {"accessKey": "", "secretKey": "", "region": "", "service": "", "sessionToken": "", "addAuthDataToQuery": false}, "ntlm": {"username": "", "password": "", "domain": "", "workstation": "", "disableRetryRequest": false}, "edgegrid": {"accessToken": "", "clientToken": "", "clientSecret": "", "nonce": "", "timestamp": "", "baseURi": "", "headersToSign": ""}, "oauth1": {"consumerKey": "", "consumerSecret": "", "signatureMethod": "HMAC-SHA1", "addEmptyParamsToSign": true, "includeBodyHash": true, "addParamsToHeader": false, "disableHeaderEncoding": false, "realm": "", "version": "1.0", "nonce": "", "timestamp": "", "verifier": "", "callback": "", "tokenSecret": "", "token": ""}, "jwt": {"addTokenTo": "header", "algorithm": "HS256", "secret": "", "isSecretBase64Encoded": false, "payload": "", "headerPrefix": "Bearer", "queryParamKey": "token", "header": ""}, "asap": {"alg": "HS256", "iss": "", "aud": "", "kid": "", "privateKey": "", "sub": "", "claims": "", "exp": ""}}, "body": {"mode": "json", "parameter": [], "raw": "{\n\t\"DeviceId\": \"\",\n\t\"DeviceInfo\": {\n\t\t\"AndroidId\": \"\",\n\t\t\"ImeI\": \"\",\n\t\t\"Manufacturer\": \"\",\n\t\t\"Model\": \"\"\n\t},\n\t\"Password\": \"\",\n\t\"Proxy\": \"{{SOCKS5}}\",\n\t\"Ticket\": \"{{A62_DATA}}\",\n\t\"Type\": 0,\n\t\"UserName\": \"\"\n}", "raw_parameter": [], "raw_schema": {"type": "object", "properties": {}}, "binary": {}}, "pre_tasks": [], "post_tasks": [], "header": {"parameter": []}, "query": {"query_add_equal": 1, "parameter": [{"param_id": "451b2e5fcd305", "description": "", "field_type": "String", "is_checked": 1, "key": "key", "not_null": -1, "value": "{{TOKEN_KEY}}"}]}, "cookie": {"parameter": []}, "restful": {"parameter": []}}, "response": {"example": [{"example_id": "451b2e5fcd306", "raw": "", "raw_parameter": [], "expect": {"code": "200", "content_type": "json", "is_default": 1, "mock": "", "name": "成功", "schema": {"type": "object", "properties": {}}, "verify_type": "schema"}}], "is_check_result": 1}, "mock_server_enable": -1, "attribute_info": {}, "tags": []}, {"target_id": "451b2e57cd2c5", "project_id": "451b2e53cd29b", "parent_id": "451b2e53cd29f", "target_type": "api", "name": "获取验证码", "version": 1, "sort": 14000, "method": "POST", "url": "login/WxBindOpMobileForReg?key={{TOKEN_KEY}}", "protocol": "http/1.1", "mark_id": "1", "description": "", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>", "kv": {"key": "", "value": "", "in": "header"}, "bearer": {"key": ""}, "basic": {"username": "", "password": ""}, "digest": {"username": "", "password": "", "realm": "", "nonce": "", "algorithm": "", "qop": "", "nc": "", "cnonce": "", "opaque": ""}, "hawk": {"authId": "", "authKey": "", "algorithm": "", "user": "", "nonce": "", "extraData": "", "default": "", "delegation": "", "timestamp": "", "includePayloadHash": false}, "awsv4": {"accessKey": "", "secretKey": "", "region": "", "service": "", "sessionToken": "", "addAuthDataToQuery": false}, "ntlm": {"username": "", "password": "", "domain": "", "workstation": "", "disableRetryRequest": false}, "edgegrid": {"accessToken": "", "clientToken": "", "clientSecret": "", "nonce": "", "timestamp": "", "baseURi": "", "headersToSign": ""}, "oauth1": {"consumerKey": "", "consumerSecret": "", "signatureMethod": "HMAC-SHA1", "addEmptyParamsToSign": true, "includeBodyHash": true, "addParamsToHeader": false, "disableHeaderEncoding": false, "realm": "", "version": "1.0", "nonce": "", "timestamp": "", "verifier": "", "callback": "", "tokenSecret": "", "token": ""}, "jwt": {"addTokenTo": "header", "algorithm": "HS256", "secret": "", "isSecretBase64Encoded": false, "payload": "", "headerPrefix": "Bearer", "queryParamKey": "token", "header": ""}, "asap": {"alg": "HS256", "iss": "", "aud": "", "kid": "", "privateKey": "", "sub": "", "claims": "", "exp": ""}}, "body": {"mode": "json", "parameter": [], "raw": "{\n\t\"OpCode\": 0,\n\t\"PhoneNumber\": \"\",\n\t\"Proxy\": \"{{SOCKS5}}\",\n\t\"Reg\": 0,\n\t\"VerifyCode\": \"\"\n}", "raw_parameter": [], "raw_schema": {"type": "object", "properties": {}}, "binary": {}}, "pre_tasks": [], "post_tasks": [], "header": {"parameter": []}, "query": {"query_add_equal": 1, "parameter": [{"param_id": "451b2e5fcd30f", "description": "", "field_type": "String", "is_checked": 1, "key": "key", "not_null": -1, "value": "{{TOKEN_KEY}}"}]}, "cookie": {"parameter": []}, "restful": {"parameter": []}}, "response": {"example": [{"example_id": "451b2e5fcd310", "raw": "", "raw_parameter": [], "expect": {"code": "200", "content_type": "json", "is_default": 1, "mock": "", "name": "成功", "schema": {"type": "object", "properties": {}}, "verify_type": "schema"}}], "is_check_result": 1}, "mock_server_enable": -1, "attribute_info": {}, "tags": []}, {"target_id": "451b2e57cd2c6", "project_id": "451b2e53cd29b", "parent_id": "451b2e53cd29f", "target_type": "api", "name": "短信登录", "version": 1, "sort": 15000, "method": "POST", "url": "login/SmsLogin?key={{TOKEN_KEY}}", "protocol": "http/1.1", "mark_id": "1", "description": "", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>", "kv": {"key": "", "value": "", "in": "header"}, "bearer": {"key": ""}, "basic": {"username": "", "password": ""}, "digest": {"username": "", "password": "", "realm": "", "nonce": "", "algorithm": "", "qop": "", "nc": "", "cnonce": "", "opaque": ""}, "hawk": {"authId": "", "authKey": "", "algorithm": "", "user": "", "nonce": "", "extraData": "", "default": "", "delegation": "", "timestamp": "", "includePayloadHash": false}, "awsv4": {"accessKey": "", "secretKey": "", "region": "", "service": "", "sessionToken": "", "addAuthDataToQuery": false}, "ntlm": {"username": "", "password": "", "domain": "", "workstation": "", "disableRetryRequest": false}, "edgegrid": {"accessToken": "", "clientToken": "", "clientSecret": "", "nonce": "", "timestamp": "", "baseURi": "", "headersToSign": ""}, "oauth1": {"consumerKey": "", "consumerSecret": "", "signatureMethod": "HMAC-SHA1", "addEmptyParamsToSign": true, "includeBodyHash": true, "addParamsToHeader": false, "disableHeaderEncoding": false, "realm": "", "version": "1.0", "nonce": "", "timestamp": "", "verifier": "", "callback": "", "tokenSecret": "", "token": ""}, "jwt": {"addTokenTo": "header", "algorithm": "HS256", "secret": "", "isSecretBase64Encoded": false, "payload": "", "headerPrefix": "Bearer", "queryParamKey": "token", "header": ""}, "asap": {"alg": "HS256", "iss": "", "aud": "", "kid": "", "privateKey": "", "sub": "", "claims": "", "exp": ""}}, "body": {"mode": "json", "parameter": [], "raw": "{\n\t\"DeviceId\": \"\",\n\t\"DeviceInfo\": {\n\t\t\"AndroidId\": \"\",\n\t\t\"ImeI\": \"\",\n\t\t\"Manufacturer\": \"\",\n\t\t\"Model\": \"\"\n\t},\n\t\"Password\": \"\",\n\t\"Proxy\": \"{{SOCKS5}}\",\n\t\"Ticket\": \"\",\n\t\"Type\": 0,\n\t\"UserName\": \"\"\n}", "raw_parameter": [], "raw_schema": {"type": "object", "properties": {}}, "binary": {}}, "pre_tasks": [], "post_tasks": [], "header": {"parameter": []}, "query": {"query_add_equal": 1, "parameter": [{"param_id": "451b2e5fcd311", "description": "", "field_type": "String", "is_checked": 1, "key": "key", "not_null": -1, "value": "{{TOKEN_KEY}}"}]}, "cookie": {"parameter": []}, "restful": {"parameter": []}}, "response": {"example": [{"example_id": "451b2e5fcd312", "raw": "", "raw_parameter": [], "expect": {"code": "200", "content_type": "json", "is_default": 1, "mock": "", "name": "成功", "schema": {"type": "object", "properties": {}}, "verify_type": "schema"}}], "is_check_result": 1}, "mock_server_enable": -1, "attribute_info": {}, "tags": []}, {"target_id": "451b2e57cd2c7", "project_id": "451b2e53cd29b", "parent_id": "451b2e53cd29f", "target_type": "api", "name": "辅助新手机登录", "version": 1, "sort": 16000, "method": "POST", "url": "login/PhoneDeviceLogin?key={{TOKEN_KEY}}", "protocol": "http/1.1", "mark_id": "1", "description": "", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>", "kv": {"key": "", "value": "", "in": "header"}, "bearer": {"key": ""}, "basic": {"username": "", "password": ""}, "digest": {"username": "", "password": "", "realm": "", "nonce": "", "algorithm": "", "qop": "", "nc": "", "cnonce": "", "opaque": ""}, "hawk": {"authId": "", "authKey": "", "algorithm": "", "user": "", "nonce": "", "extraData": "", "default": "", "delegation": "", "timestamp": "", "includePayloadHash": false}, "awsv4": {"accessKey": "", "secretKey": "", "region": "", "service": "", "sessionToken": "", "addAuthDataToQuery": false}, "ntlm": {"username": "", "password": "", "domain": "", "workstation": "", "disableRetryRequest": false}, "edgegrid": {"accessToken": "", "clientToken": "", "clientSecret": "", "nonce": "", "timestamp": "", "baseURi": "", "headersToSign": ""}, "oauth1": {"consumerKey": "", "consumerSecret": "", "signatureMethod": "HMAC-SHA1", "addEmptyParamsToSign": true, "includeBodyHash": true, "addParamsToHeader": false, "disableHeaderEncoding": false, "realm": "", "version": "1.0", "nonce": "", "timestamp": "", "verifier": "", "callback": "", "tokenSecret": "", "token": ""}, "jwt": {"addTokenTo": "header", "algorithm": "HS256", "secret": "", "isSecretBase64Encoded": false, "payload": "", "headerPrefix": "Bearer", "queryParamKey": "token", "header": ""}, "asap": {"alg": "HS256", "iss": "", "aud": "", "kid": "", "privateKey": "", "sub": "", "claims": "", "exp": ""}}, "body": {"mode": "json", "parameter": [], "raw": "{\n\t\"Url\": \"\"\n}", "raw_parameter": [], "raw_schema": {"type": "object", "properties": {}}, "binary": {}}, "pre_tasks": [], "post_tasks": [], "header": {"parameter": []}, "query": {"query_add_equal": 1, "parameter": [{"param_id": "451b2e5fcd315", "description": "", "field_type": "String", "is_checked": 1, "key": "key", "not_null": -1, "value": "{{TOKEN_KEY}}"}]}, "cookie": {"parameter": []}, "restful": {"parameter": []}}, "response": {"example": [{"example_id": "451b2e5fcd316", "raw": "", "raw_parameter": [], "expect": {"code": "200", "content_type": "json", "is_default": 1, "mock": "", "name": "成功", "schema": {"type": "object", "properties": {}}, "verify_type": "schema"}}], "is_check_result": 1}, "mock_server_enable": -1, "attribute_info": {}, "tags": []}, {"target_id": "451b2e57cd2c8", "project_id": "451b2e53cd29b", "parent_id": "451b2e53cd29f", "target_type": "api", "name": "唤醒登录", "version": 1, "sort": 17000, "method": "GET", "url": "/login/WakeUpLogin?key={{TOKEN_KEY}}", "protocol": "http/1.1", "mark_id": "1", "description": "", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>", "kv": {"key": "", "value": "", "in": "header"}, "bearer": {"key": ""}, "basic": {"username": "", "password": ""}, "digest": {"username": "", "password": "", "realm": "", "nonce": "", "algorithm": "", "qop": "", "nc": "", "cnonce": "", "opaque": ""}, "hawk": {"authId": "", "authKey": "", "algorithm": "", "user": "", "nonce": "", "extraData": "", "default": "", "delegation": "", "timestamp": "", "includePayloadHash": false}, "awsv4": {"accessKey": "", "secretKey": "", "region": "", "service": "", "sessionToken": "", "addAuthDataToQuery": false}, "ntlm": {"username": "", "password": "", "domain": "", "workstation": "", "disableRetryRequest": false}, "edgegrid": {"accessToken": "", "clientToken": "", "clientSecret": "", "nonce": "", "timestamp": "", "baseURi": "", "headersToSign": ""}, "oauth1": {"consumerKey": "", "consumerSecret": "", "signatureMethod": "HMAC-SHA1", "addEmptyParamsToSign": true, "includeBodyHash": true, "addParamsToHeader": false, "disableHeaderEncoding": false, "realm": "", "version": "1.0", "nonce": "", "timestamp": "", "verifier": "", "callback": "", "tokenSecret": "", "token": ""}, "jwt": {"addTokenTo": "header", "algorithm": "HS256", "secret": "", "isSecretBase64Encoded": false, "payload": "", "headerPrefix": "Bearer", "queryParamKey": "token", "header": ""}, "asap": {"alg": "HS256", "iss": "", "aud": "", "kid": "", "privateKey": "", "sub": "", "claims": "", "exp": ""}}, "body": {"mode": "json", "parameter": [], "raw": "", "raw_parameter": [], "raw_schema": {"type": "object", "properties": {}}, "binary": {}}, "pre_tasks": [], "post_tasks": [], "header": {"parameter": []}, "query": {"query_add_equal": 1, "parameter": [{"param_id": "451b2e5fcd317", "description": "", "field_type": "String", "is_checked": 1, "key": "key", "not_null": -1, "value": "{{TOKEN_KEY}}"}]}, "cookie": {"parameter": []}, "restful": {"parameter": []}}, "response": {"example": [{"example_id": "451b2e5fcd318", "raw": "", "raw_parameter": [], "expect": {"code": "200", "content_type": "json", "is_default": 1, "mock": "", "name": "成功", "schema": {"type": "object", "properties": {}}, "verify_type": "schema"}}], "is_check_result": 1}, "mock_server_enable": -1, "attribute_info": {}, "tags": []}, {"target_id": "451b2e57cd2c9", "project_id": "451b2e53cd29b", "parent_id": "451b2e53cd29f", "target_type": "api", "name": "退出登录", "version": 1, "sort": 18000, "method": "GET", "url": "/login/LogOut?key={{TOKEN_KEY}}", "protocol": "http/1.1", "mark_id": "1", "description": "", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>", "kv": {"key": "", "value": "", "in": "header"}, "bearer": {"key": ""}, "basic": {"username": "", "password": ""}, "digest": {"username": "", "password": "", "realm": "", "nonce": "", "algorithm": "", "qop": "", "nc": "", "cnonce": "", "opaque": ""}, "hawk": {"authId": "", "authKey": "", "algorithm": "", "user": "", "nonce": "", "extraData": "", "default": "", "delegation": "", "timestamp": "", "includePayloadHash": false}, "awsv4": {"accessKey": "", "secretKey": "", "region": "", "service": "", "sessionToken": "", "addAuthDataToQuery": false}, "ntlm": {"username": "", "password": "", "domain": "", "workstation": "", "disableRetryRequest": false}, "edgegrid": {"accessToken": "", "clientToken": "", "clientSecret": "", "nonce": "", "timestamp": "", "baseURi": "", "headersToSign": ""}, "oauth1": {"consumerKey": "", "consumerSecret": "", "signatureMethod": "HMAC-SHA1", "addEmptyParamsToSign": true, "includeBodyHash": true, "addParamsToHeader": false, "disableHeaderEncoding": false, "realm": "", "version": "1.0", "nonce": "", "timestamp": "", "verifier": "", "callback": "", "tokenSecret": "", "token": ""}, "jwt": {"addTokenTo": "header", "algorithm": "HS256", "secret": "", "isSecretBase64Encoded": false, "payload": "", "headerPrefix": "Bearer", "queryParamKey": "token", "header": ""}, "asap": {"alg": "HS256", "iss": "", "aud": "", "kid": "", "privateKey": "", "sub": "", "claims": "", "exp": ""}}, "body": {"mode": "json", "parameter": [], "raw": "", "raw_parameter": [], "raw_schema": {"type": "object", "properties": {}}, "binary": {}}, "pre_tasks": [], "post_tasks": [], "header": {"parameter": []}, "query": {"query_add_equal": 1, "parameter": [{"param_id": "451b2e5fcd319", "description": "", "field_type": "String", "is_checked": 1, "key": "key", "not_null": -1, "value": "{{TOKEN_KEY}}"}]}, "cookie": {"parameter": []}, "restful": {"parameter": []}}, "response": {"example": [{"example_id": "451b2e5fcd31a", "raw": "", "raw_parameter": [], "expect": {"code": "200", "content_type": "json", "is_default": 1, "mock": "", "name": "成功", "schema": {"type": "object", "properties": {}}, "verify_type": "schema"}}], "is_check_result": 1}, "mock_server_enable": -1, "attribute_info": {}, "tags": []}], "samples": [], "automated_testings": []}