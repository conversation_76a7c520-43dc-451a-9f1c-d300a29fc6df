basePath: /v849
definitions:
  ActionInfo:
    properties:
      AppMsg:
        $ref: '#/definitions/AppMsg'
    title: ActionInfo
    type: object
  AlisaModel:
    properties:
      Alisa:
        example: ""
        type: string
    title: AlisaModel
    type: object
  AppInfo:
    properties:
      AppName:
        example: ""
        type: string
      FromURL:
        example: ""
        type: string
      ID:
        example: ""
        type: string
      InstallURL:
        example: ""
        type: string
      IsForceUpdate:
        format: uint32
        type: integer
      Version:
        example: ""
        type: string
    title: AppInfo
    type: object
  AppMessageItem:
    properties:
      ContentType:
        description: ' 2001:(红包消息)'
        format: uint32
        type: integer
      ContentXML:
        example: ""
        type: string
      ToUserName:
        example: ""
        type: string
    title: AppMessageItem
    type: object
  AppMessageModel:
    properties:
      AppList:
        items:
          $ref: '#/definitions/AppMessageItem'
        type: array
    title: AppMessageModel
    type: object
  AppMsg:
    properties:
      MessageAction:
        example: ""
        type: string
    title: AppMsg
    type: object
  AppletModel:
    properties:
      AppId:
        description: ' 应用ID'
        example: ""
        type: string
      Data:
        description: ' 小程序云函数操作的 Data; json字符串, 注意必须是 json 字符串; 传空时默认值为: ''{"with_credentials":true,"from_component":true,"data":{"lang":"zh_CN"},"api_name":"webapi_getuserinfo"}'''
        example: ""
        type: string
      Opt:
        description: ' 小程序云函数操作的 Opt; 默认为1'
        example: "1"
        format: int32
        type: integer
      PackageName:
        example: ""
        type: string
      SdkName:
        example: ""
        type: string
    title: AppletModel
    type: object
  BatchGetContactModel:
    properties:
      RoomWxIDList:
        items:
          type: string
        type: array
      UserNames:
        items:
          type: string
        type: array
    title: BatchGetContactModel
    type: object
  CdnUploadVideoRequest:
    properties:
      ThumbData:
        description: ' ThumbData'
        example: ""
        type: string
      ToUserName:
        example: ""
        type: string
      VideoData:
        description: ' 视频数据'
        items:
          format: int
          type: integer
        type: array
    title: CdnUploadVideoRequest
    type: object
  ChatRoomWxIdListModel:
    properties:
      ChatRoomWxIdList:
        items:
          type: string
        type: array
    title: ChatRoomWxIdListModel
    type: object
  ChatroomMemberModel:
    properties:
      ChatRoomName:
        description: ' 群聊ID：xxx@chatroom'
        example: ""
        type: string
      UserList:
        items:
          type: string
        type: array
    title: ChatroomMemberModel
    type: object
  ChatroomNameModel:
    properties:
      ChatRoomName:
        description: ' 群聊ID：xxx@chatroom'
        example: ""
        type: string
      Nickname:
        example: ""
        type: string
    title: ChatroomNameModel
    type: object
  CollectmoneyModel:
    properties:
      InvalidTime:
        example: ""
        type: string
      ToUserName:
        example: ""
        type: string
      TransFerId:
        example: ""
        type: string
      TransactionId:
        example: ""
        type: string
    title: CollectmoneyModel
    type: object
  ConfirmPreTransfer:
    properties:
      BankSerial:
        description: ' 付款方式 Serial序列号'
        example: ""
        type: string
      BankType:
        description: ' 付款方式 类型'
        example: ""
        type: string
      PayPassword:
        description: ' 支付密码'
        example: ""
        type: string
      ReqKey:
        description: ' 创建转账返回的ReqKey'
        example: ""
        type: string
    title: ConfirmPreTransfer
    type: object
  ContentObject:
    properties:
      ContentStyle:
        format: uint32
        type: integer
      ContentURL:
        example: ""
        type: string
      Description:
        example: ""
        type: string
      MediaList:
        $ref: '#/definitions/MediaList'
      Title:
        example: ""
        type: string
    title: ContentObject
    type: object
  CreateChatRoomModel:
    properties:
      TopIc:
        example: ""
        type: string
      UserList:
        items:
          type: string
        type: array
    title: CreateChatRoomModel
    type: object
  CreatePreTransfer:
    properties:
      Description:
        description: ' 转账备注'
        example: ""
        type: string
      Fee:
        description: ' 转账金额(单位为分)'
        format: uint32
        type: integer
      ToUserName:
        description: ' 要转账用户的wxid'
        example: ""
        type: string
    title: CreatePreTransfer
    type: object
  DataSection:
    properties:
      DataLen:
        description: ' 数据分包长度(不要超过 65535)'
        example: "61440"
        format: uint32
        type: integer
      StartPos:
        description: ' 数据分包开始位置'
        format: uint32
        type: integer
    title: DataSection
    type: object
  DelContactModel:
    properties:
      DelUserName:
        example: ""
        type: string
    title: DelContactModel
    type: object
  DelSafeDeviceModel:
    properties:
      DeviceUUID:
        example: ""
        type: string
    title: DelSafeDeviceModel
    type: object
  DelayAuthKeyModel:
    properties:
      Days:
        description: ' AuthKey 的延期天数; Days 小于1默认设置为30'
        example: "30"
        format: int
        type: integer
      ExpiryDate:
        description: ' AuthKey 的到期日期(例如: 2024-01-01); 与 Days 参数只能选其一(优先使用 ExpiryDate
          参数)'
        example: ""
        type: string
      Key:
        description: ' 要延期的 AuthKey'
        example: ""
        type: string
    title: DelayAuthKeyModel
    type: object
  DeleteAuthKeyModel:
    properties:
      Key:
        description: ' 要删除的 AuthKey'
        example: ""
        type: string
      Opt:
        description: ' 删除操作 0:仅删除授权码 1:删除授权码相关的所有数据'
        format: int
        type: integer
    title: DeleteAuthKeyModel
    type: object
  DeviceIdLoginModel:
    properties:
      DeviceInfo:
        $ref: '#/definitions/DeviceInfo'
        description: ' 设备信息'
      LoginData:
        description: ' 62 数据/A16 数据'
        example: ""
        type: string
      Password:
        description: ' 微信密码'
        example: ""
        type: string
      Proxy:
        description: ' socks代理，例如：socks5://username:password@ipv4:port'
        example: ""
        type: string
      Ticket:
        description: ' SMS短信验证码'
        example: ""
        type: string
      Type:
        format: int
        type: integer
      UserName:
        description: ' 手机号'
        example: ""
        type: string
    title: DeviceIdLoginModel
    type: object
  DeviceInfo:
    properties:
      AdSource:
        example: ""
        type: string
      BundleID:
        example: ""
        type: string
      CarrierName:
        example: ""
        type: string
      ClientCheckDataXML:
        example: ""
        type: string
      CoreCount:
        format: uint32
        type: integer
      DeviceBrand:
        example: ""
        type: string
      DeviceID:
        items:
          format: int
          type: integer
        type: array
      DeviceName:
        example: ""
        type: string
      DeviceToken:
        $ref: '#/definitions/wechat.TrustResp'
      GUID2:
        example: ""
        type: string
      Imei:
        example: ""
        type: string
      IphoneVer:
        example: ""
        type: string
      Language:
        example: ""
        type: string
      OsType:
        example: ""
        type: string
      OsTypeNumber:
        example: ""
        type: string
      RealCountry:
        example: ""
        type: string
      SoftTypeXML:
        example: ""
        type: string
      TimeZone:
        example: ""
        type: string
      UUIDOne:
        example: ""
        type: string
      UUIDTwo:
        example: ""
        type: string
    title: DeviceInfo
    type: object
  DownMediaModel:
    properties:
      AesKey:
        example: ""
        type: string
      FileType:
        format: uint32
        type: integer
      FileURL:
        example: ""
        type: string
    title: DownMediaModel
    type: object
  DownloadMediaModel:
    properties:
      Key:
        example: ""
        type: string
      URL:
        example: ""
        type: string
    title: DownloadMediaModel
    type: object
  DownloadParam:
    properties:
      CompressType:
        description: ' 下载图片时，数据压缩类型(默认为0即可)'
        format: int
        type: integer
      FromUserName:
        description: ' 下载图片时，图片消息的发送者'
        example: ""
        type: string
      MsgId:
        description: ' 消息ID(注意是msg_id 不是new_msg_id)'
        format: uint32
        type: integer
      Section:
        $ref: '#/definitions/DataSection'
        description: ' 当前要获取的数据分包'
      ToUserName:
        description: ' 下载图片时，图片消息的接收者'
        example: ""
        type: string
      TotalLen:
        description: ' 下载数据的总长度'
        format: int
        type: integer
    title: DownloadParam
    type: object
  DownloadVoiceModel:
    properties:
      Bufid:
        example: ""
        type: string
      Length:
        format: int
        type: integer
      NewMsgId:
        example: ""
        type: string
      ToUserName:
        example: ""
        type: string
    title: DownloadVoiceModel
    type: object
  Enc:
    properties:
      Key:
        example: ""
        type: string
      Value:
        format: uint32
        type: integer
    title: Enc
    type: object
  ExtDeviceLoginModel:
    properties:
      QrConnect:
        example: ""
        type: string
    title: ExtDeviceLoginModel
    type: object
  FavInfoModel:
    properties:
      FavId:
        format: uint32
        type: integer
      KeyBuf:
        example: ""
        type: string
    title: FavInfoModel
    type: object
  FinderFollowModel:
    properties:
      Cook:
        example: ""
        type: string
      FinderUserName:
        example: ""
        type: string
      OpType:
        format: int32
        type: integer
      PosterUsername:
        example: ""
        type: string
      RefObjectId:
        example: ""
        type: string
      Userver:
        format: int32
        type: integer
    title: FinderFollowModel
    type: object
  FinderSearchModel:
    properties:
      Index:
        format: uint32
        type: integer
      UserKey:
        example: ""
        type: string
      Userver:
        format: int32
        type: integer
      Uuid:
        example: ""
        type: string
    title: FinderSearchModel
    type: object
  FinderUserPrepareModel:
    properties:
      Userver:
        format: int32
        type: integer
    title: FinderUserPrepareModel
    type: object
  FollowGHModel:
    properties:
      GHList:
        items:
          $ref: '#/definitions/VerifyUserItem'
        type: array
    title: FollowGHModel
    type: object
  ForwardImageItem:
    properties:
      AesKey:
        example: ""
        type: string
      CdnMidImgSize:
        format: int32
        type: integer
      CdnMidImgUrl:
        example: ""
        type: string
      CdnThumbImgSize:
        format: int32
        type: integer
      ToUserName:
        example: ""
        type: string
    title: ForwardImageItem
    type: object
  ForwardMessageModel:
    properties:
      ForwardImageList:
        items:
          $ref: '#/definitions/ForwardImageItem'
        type: array
      ForwardVideoList:
        items:
          $ref: '#/definitions/ForwardVideoItem'
        type: array
    title: ForwardMessageModel
    type: object
  ForwardVideoItem:
    properties:
      AesKey:
        example: ""
        type: string
      CdnThumbLength:
        format: int
        type: integer
      CdnVideoUrl:
        example: ""
        type: string
      Length:
        format: int
        type: integer
      PlayLength:
        format: int
        type: integer
      ToUserName:
        example: ""
        type: string
    title: ForwardVideoItem
    type: object
  GenAuthKeyModel:
    properties:
      Count:
        description: ' 要生成 AuthKey 的个数; Count小于1默认设置为1'
        example: "1"
        format: int
        type: integer
      Days:
        description: ' AuthKey 的过期天数; Days小于1默认设置为30'
        example: "30"
        format: int
        type: integer
    title: GenAuthKeyModel
    type: object
  GeneratePayQCodeModel:
    properties:
      Money:
        description: ' 金额(单位为分), 999 即为 9.99 元'
        example: ""
        type: string
      Name:
        description: ' 收款备注'
        example: ""
        type: string
    title: GeneratePayQCodeModel
    type: object
  GetA8KeyRequestModel:
    properties:
      OpCode:
        format: uint32
        type: integer
      ReqUrl:
        example: ""
        type: string
      Scene:
        format: uint32
        type: integer
    title: GetA8KeyRequestModel
    type: object
  GetChatroomMemberDetailModel:
    properties:
      ChatRoomName:
        description: ' 群聊ID：xxx@chatroom'
        example: ""
        type: string
    title: GetChatroomMemberDetailModel
    type: object
  GetChatroomQrCodeModel:
    properties:
      ChatRoomName:
        description: ' 群聊ID：xxx@chatroom'
        example: ""
        type: string
    title: GetChatroomQrCodeModel
    type: object
  GetContactListModel:
    properties:
      CurrentChatRoomContactSeq:
        format: uint32
        type: integer
      CurrentWxcontactSeq:
        format: uint32
        type: integer
    title: GetContactListModel
    type: object
  GetFriendRelationModel:
    properties:
      UserName:
        example: ""
        type: string
    title: GetFriendRelationModel
    type: object
  GetIdDetailModel:
    properties:
      BlackList:
        items:
          type: string
        type: array
      Id:
        example: ""
        type: string
      Location:
        $ref: '#/definitions/baseinfo.Location'
      LocationVal:
        format: int64
        type: integer
    title: GetIdDetailModel
    type: object
  GetLoginQrCodeModel:
    properties:
      Check:
        description: ' 修改代理时(SetProxy接口) 是否发送检测代理请求(可能导致请求超时)'
        example: "false"
        type: boolean
      Proxy:
        description: ' socks代理，例如：socks5://username:password@ipv4:port'
        example: ""
        type: string
    title: GetLoginQrCodeModel
    type: object
  GetMpA8KeyModel:
    properties:
      Opcode:
        format: uint32
        type: integer
      Scene:
        format: int64
        type: integer
      Url:
        example: ""
        type: string
    title: GetMpA8KeyModel
    type: object
  GetMpHistoryMsgModel:
    properties:
      Url:
        example: ""
        type: string
    title: GetMpHistoryMsgModel
    type: object
  GetQrCodeModel:
    properties:
      Recover:
        description: ' 保持默认值, 无需修改'
        type: boolean
      Style:
        description: ' 个人二维码样式: 可设置为8, 其余自行探索'
        example: "8"
        format: uint32
        type: integer
    title: GetQrCodeModel
    type: object
  GetRedPacketList:
    properties:
      HongBaoItem:
        $ref: '#/definitions/HongBaoURLItem'
      Limit:
        format: int64
        type: integer
      NativeURL:
        example: ""
        type: string
      Offset:
        format: int64
        type: integer
    title: GetRedPacketList
    type: object
  GetSnsInfoModel:
    properties:
      FirstPageMD5:
        example: ""
        type: string
      MaxID:
        format: uint64
        type: integer
      UserName:
        example: ""
        type: string
    title: GetSnsInfoModel
    type: object
  GetSyncMsgModel:
    properties:
      Key:
        example: ""
        type: string
    title: GetSyncMsgModel
    type: object
  GroupListModel:
    properties:
      Key:
        example: ""
        type: string
    title: GroupListModel
    type: object
  GroupMassMsgImageModel:
    properties:
      ImageBase64:
        example: ""
        type: string
      ToUserName:
        items:
          type: string
        type: array
    title: GroupMassMsgImageModel
    type: object
  GroupMassMsgTextModel:
    properties:
      Content:
        example: ""
        type: string
      ToUserName:
        items:
          type: string
        type: array
    title: GroupMassMsgTextModel
    type: object
  HongBaoItem:
    properties:
      Limit:
        format: int64
        type: integer
      NativeURL:
        example: ""
        type: string
      URLItem:
        $ref: '#/definitions/HongBaoURLItem'
    title: HongBaoItem
    type: object
  HongBaoURLItem:
    properties:
      ChannelID:
        example: ""
        type: string
      MsgType:
        example: ""
        type: string
      SendID:
        example: ""
        type: string
      SendUserName:
        example: ""
        type: string
      ShowSourceMac:
        example: ""
        type: string
      ShowWxPayTitle:
        example: ""
        type: string
      Sign:
        example: ""
        type: string
      Ver:
        example: ""
        type: string
    title: HongBaoURLItem
    type: object
  InviteChatroomMembersModel:
    properties:
      ChatRoomName:
        description: ' 群聊ID：xxx@chatroom'
        example: ""
        type: string
      UserList:
        items:
          type: string
        type: array
    title: InviteChatroomMembersModel
    type: object
  LabelModel:
    properties:
      LabelId:
        example: ""
        type: string
      LabelNameList:
        items:
          type: string
        type: array
      UserLabelList:
        items:
          $ref: '#/definitions/baseinfo.UserLabelInfoItem'
        type: array
    title: LabelModel
    type: object
  Location:
    properties:
      City:
        example: ""
        type: string
      Latitude:
        example: ""
        type: string
      Longitude:
        example: ""
        type: string
      PoiAddress:
        example: ""
        type: string
      PoiClassifyID:
        example: ""
        type: string
      PoiClassifyType:
        format: uint32
        type: integer
      PoiName:
        example: ""
        type: string
    title: Location
    type: object
  Media:
    properties:
      Description:
        example: ""
        type: string
      Enc:
        $ref: '#/definitions/Enc'
      ID:
        format: uint64
        type: integer
      Private:
        format: uint32
        type: integer
      Size:
        $ref: '#/definitions/Size'
      SubType:
        format: uint32
        type: integer
      Thumb:
        $ref: '#/definitions/Thumb'
      Title:
        example: ""
        type: string
      Type:
        format: uint32
        type: integer
      URL:
        $ref: '#/definitions/URL'
      UserData:
        example: ""
        type: string
      VideoDuration:
        format: double
        type: number
      VideoSize:
        $ref: '#/definitions/VideoSize'
    title: Media
    type: object
  MediaList:
    properties:
      Media:
        items:
          $ref: '#/definitions/Media'
        type: array
    title: MediaList
    type: object
  MessageItem:
    properties:
      AtWxIDList:
        description: ' 发送艾特消息时的 wxid 列表'
        items:
          type: string
        type: array
      ImageContent:
        description: ' 图片类型消息时图片的 base64 编码'
        example: ""
        type: string
      MsgType:
        description: 1 Text 2 Image
        format: int
        type: integer
      TextContent:
        description: ' 文本类型消息时内容'
        example: ""
        type: string
      ToUserName:
        description: ' 接收者 wxid'
        example: ""
        type: string
    title: MessageItem
    type: object
  ModifyUserInfo:
    properties:
      City:
        example: ""
        type: string
      Country:
        example: ""
        type: string
      InitFlag:
        format: uint32
        type: integer
      NickName:
        example: ""
        type: string
      Province:
        example: ""
        type: string
      Sex:
        format: uint32
        type: integer
      Signature:
        example: ""
        type: string
    title: ModifyUserInfo
    type: object
  MoveContractModel:
    properties:
      ChatRoomName:
        description: ' 群聊ID：xxx@chatroom'
        example: ""
        type: string
      Val:
        format: uint32
        type: integer
    title: MoveContractModel
    type: object
  OpenRedEnvelopesModel:
    properties:
      NativeUrl:
        example: ""
        type: string
    title: OpenRedEnvelopesModel
    type: object
  PeopleNearbyModel:
    properties:
      Latitude:
        format: double
        type: number
      Longitude:
        format: double
        type: number
    title: PeopleNearbyModel
    type: object
  PhoneLoginModel:
    properties:
      Url:
        example: ""
        type: string
    title: PhoneLoginModel
    type: object
  QRConnectAuthorizeModel:
    properties:
      QrUrl:
        example: ""
        type: string
    title: QRConnectAuthorizeModel
    type: object
  QWAcceptChatRoomModel:
    properties:
      Link:
        example: ""
        type: string
      Opcode:
        format: uint32
        type: integer
    title: QWAcceptChatRoomModel
    type: object
  QWAddChatRoomMemberModel:
    properties:
      ChatRoomName:
        description: ' 群聊ID：xxx@chatroom'
        example: ""
        type: string
      ToUserName:
        items:
          type: string
        type: array
    title: QWAddChatRoomMemberModel
    type: object
  QWAdminAcceptJoinChatRoomSetModel:
    properties:
      ChatRoomName:
        description: ' 群聊ID：xxx@chatroom'
        example: ""
        type: string
      P:
        format: int64
        type: integer
    title: QWAdminAcceptJoinChatRoomSetModel
    type: object
  QWApplyAddContactModel:
    properties:
      Content:
        example: ""
        type: string
      UserName:
        example: ""
        type: string
      V1:
        example: ""
        type: string
    title: QWApplyAddContactModel
    type: object
  QWChatRoomTransferOwnerModel:
    properties:
      ChatRoomName:
        description: ' 群聊ID：xxx@chatroom'
        example: ""
        type: string
      ToUserName:
        example: ""
        type: string
    title: QWChatRoomTransferOwnerModel
    type: object
  QWContactModel:
    properties:
      ChatRoom:
        example: ""
        type: string
      T:
        example: ""
        type: string
      ToUserName:
        example: ""
        type: string
    title: QWContactModel
    type: object
  QWCreateModel:
    properties:
      ToUserName:
        items:
          type: string
        type: array
    title: QWCreateModel
    type: object
  QWModChatRoomNameModel:
    properties:
      ChatRoomName:
        description: ' 群聊ID：xxx@chatroom'
        example: ""
        type: string
      Name:
        example: ""
        type: string
    title: QWModChatRoomNameModel
    type: object
  QWRemarkModel:
    properties:
      Name:
        example: ""
        type: string
      ToUserName:
        example: ""
        type: string
    title: QWRemarkModel
    type: object
  QWSyncChatRoomModel:
    properties:
      Key:
        example: ""
        type: string
    title: QWSyncChatRoomModel
    type: object
  RedPacket:
    properties:
      Amount:
        description: ' 每个红包的金额(单位为分, 最小为100); 总金额为 Amount*Count'
        format: uint32
        type: integer
      Content:
        description: ' 红包的备注内容(祝福语)'
        example: ""
        type: string
      Count:
        description: ' 红包个数(最少为1)'
        format: uint32
        type: integer
      From:
        description: ' InAway(0:群红包; 1:个人红包)'
        format: uint32
        type: integer
      RedType:
        description: ' 红包类型(0 普通红包; 1 拼手气红包; ? 专属红包)'
        format: uint32
        type: integer
      Username:
        description: ' 红包接收者; wxid 或 群ID'
        example: ""
        type: string
    title: RedPacket
    type: object
  ReplyCommentItem:
    properties:
      NickName:
        description: ' 发表评论的昵称'
        example: ""
        type: string
      OpType:
        description: ' 操作类型：评论/点赞'
        format: uint32
        type: integer
      Source:
        description: ' source'
        format: uint32
        type: integer
      UserName:
        description: ' 评论的微信ID'
        example: ""
        type: string
    title: ReplyCommentItem
    type: object
  RevokeMsgModel:
    properties:
      ClientMsgId:
        format: uint64
        type: integer
      CreateTime:
        format: uint64
        type: integer
      NewMsgId:
        example: ""
        type: string
      ToUserName:
        example: ""
        type: string
    title: RevokeMsgModel
    type: object
  ScanIntoUrlGroupModel:
    properties:
      Url:
        example: ""
        type: string
    title: ScanIntoUrlGroupModel
    type: object
  SearchContactModel:
    properties:
      FromScene:
        format: uint64
        type: integer
      Tg:
        example: ""
        type: string
      UserName:
        example: ""
        type: string
    title: SearchContactModel
    type: object
  SearchContactRequestModel:
    properties:
      FromScene:
        format: uint32
        type: integer
      OpCode:
        description: ' 操作类型'
        format: uint32
        type: integer
      SearchScene:
        description: ' 搜索场景'
        format: uint32
        type: integer
      UserName:
        description: ' 要搜索的内容(微信号、手机号、QQ号等)'
        example: ""
        type: string
    title: SearchContactRequestModel
    type: object
  SendChangePwdRequestModel:
    properties:
      NewPass:
        example: ""
        type: string
      OldPass,:
        example: ""
        type: string
      OpCode:
        format: uint32
        type: integer
    title: SendChangePwdRequestModel
    type: object
  SendEmojiItem:
    properties:
      EmojiMd5:
        example: ""
        type: string
      EmojiSize:
        format: int32
        type: integer
      ToUserName:
        example: ""
        type: string
    title: SendEmojiItem
    type: object
  SendEmojiMessageModel:
    properties:
      EmojiList:
        items:
          $ref: '#/definitions/SendEmojiItem'
        type: array
    title: SendEmojiMessageModel
    type: object
  SendFavItemCircle:
    properties:
      BlackList:
        items:
          type: string
        type: array
      FavItemID:
        format: uint32
        type: integer
      Location:
        $ref: '#/definitions/baseinfo.Location'
      LocationVal:
        format: int64
        type: integer
      SourceID:
        example: ""
        type: string
    title: SendFavItemCircle
    type: object
  SendMessageModel:
    properties:
      MsgItem:
        description: ' 消息体数组'
        items:
          $ref: '#/definitions/MessageItem'
        type: array
    title: SendMessageModel
    type: object
  SendModifyRemarkRequestModel:
    properties:
      RemarkName:
        example: ""
        type: string
      UserName:
        example: ""
        type: string
    title: SendModifyRemarkRequestModel
    type: object
  SendPatModel:
    properties:
      ChatRoomName:
        description: ' 群聊ID：xxx@chatroom'
        example: ""
        type: string
      Scene:
        format: int64
        type: integer
      ToUserName:
        example: ""
        type: string
    title: SendPatModel
    type: object
  SendSnsCommentRequestModel:
    properties:
      SnsCommentList:
        items:
          $ref: '#/definitions/SnsCommentItem'
        type: array
      Tx:
        type: boolean
    title: SendSnsCommentRequestModel
    type: object
  SendSnsObjectOpRequestModel:
    properties:
      SnsObjectOpList:
        items:
          $ref: '#/definitions/SnsObjectOpItem'
        type: array
    title: SendSnsObjectOpRequestModel
    type: object
  SendUploadVoiceRequestModel:
    properties:
      ToUserName:
        example: ""
        type: string
      VoiceData:
        example: ""
        type: string
      VoiceFormat:
        format: int32
        type: integer
      VoiceSecond,:
        format: int32
        type: integer
    title: SendUploadVoiceRequestModel
    type: object
  SetBackgroundImageModel:
    properties:
      Url:
        example: ""
        type: string
    title: SetBackgroundImageModel
    type: object
  SetChatroomAccessVerifyModel:
    properties:
      ChatRoomName:
        description: ' 群聊ID：xxx@chatroom'
        example: ""
        type: string
      Enable:
        type: boolean
    title: SetChatroomAccessVerifyModel
    type: object
  SetFriendCircleDaysModel:
    properties:
      Function:
        format: uint32
        type: integer
      Value:
        format: uint32
        type: integer
    title: SetFriendCircleDaysModel
    type: object
  SetSendPatModel:
    properties:
      Value:
        example: ""
        type: string
    title: SetSendPatModel
    type: object
  ShareCardParam:
    properties:
      CardAlias:
        description: ' 名片别名(发送公众号名片时留空)'
        example: ""
        type: string
      CardFlag:
        description: ' 名片CertFlag(0:个人名片 24:公众号名片)'
        format: int
        type: integer
      CardNickName:
        description: ' 名片昵称'
        example: ""
        type: string
      CardWxId:
        description: ' 名片wxid'
        example: ""
        type: string
      ToUserName:
        description: ' 消息接收者'
        example: ""
        type: string
    title: ShareCardParam
    type: object
  Size:
    properties:
      Height:
        example: ""
        type: string
      TotalSize:
        example: ""
        type: string
      Width:
        example: ""
        type: string
    title: Size
    type: object
  SnsCommentItem:
    properties:
      Content:
        description: ' 评论内容'
        example: ""
        type: string
      CreateTime:
        description: ' 创建时间'
        format: uint32
        type: integer
      ItemID:
        description: ' 朋友圈项ID'
        example: ""
        type: string
      OpType:
        description: ' 操作类型：评论/点赞'
        format: uint32
        type: integer
      ReplyCommentID:
        description: ' 回复的评论ID'
        format: uint32
        type: integer
      ReplyItem:
        $ref: '#/definitions/ReplyCommentItem'
        description: ' 回覆的评论项'
      ToUserName:
        description: ' 好友微信ID'
        example: ""
        type: string
    title: SnsCommentItem
    type: object
  SnsLocationInfoModel:
    properties:
      City:
        example: ""
        type: string
      Latitude:
        example: ""
        type: string
      Longitude:
        example: ""
        type: string
      PoiAddress:
        example: ""
        type: string
      PoiClassifyID:
        example: ""
        type: string
      PoiClassifyType:
        format: uint32
        type: integer
      PoiClickableStatus:
        format: uint32
        type: integer
      PoiInfoURL:
        example: ""
        type: string
      PoiName:
        example: ""
        type: string
      PoiScale:
        format: int32
        type: integer
    title: SnsLocationInfoModel
    type: object
  SnsMediaItemModel:
    properties:
      Description:
        example: ""
        type: string
      ID:
        format: uint32
        type: integer
      MD5:
        example: ""
        type: string
      Private:
        format: uint32
        type: integer
      SizeHeight:
        example: ""
        type: string
      SizeWidth:
        example: ""
        type: string
      SubType:
        format: uint32
        type: integer
      ThumType:
        example: ""
        type: string
      Thumb:
        example: ""
        type: string
      Title:
        example: ""
        type: string
      TotalSize:
        example: ""
        type: string
      Type:
        format: uint32
        type: integer
      URL:
        example: ""
        type: string
      URLType:
        example: ""
        type: string
      UserData:
        example: ""
        type: string
      VideoDuration:
        format: double
        type: number
      VideoHeight:
        example: ""
        type: string
      VideoMD5:
        example: ""
        type: string
      VideoWidth:
        example: ""
        type: string
    title: SnsMediaItemModel
    type: object
  SnsObjectOpItem:
    properties:
      Data:
        description: ' 其它数据'
        items:
          format: int
          type: integer
        type: array
      DataLen:
        description: ' 其它数据长度'
        format: uint32
        type: integer
      Ext:
        format: uint32
        type: integer
      OpType:
        description: ' 操作码'
        format: uint32
        type: integer
      SnsObjID:
        description: ' 朋友圈ID'
        example: ""
        type: string
    title: SnsObjectOpItem
    type: object
  SnsObjectOpRequestModel:
    properties:
      SnsObjectOpList:
        items:
          $ref: '#/definitions/SnsObjectOpItem'
        type: array
    title: SnsObjectOpRequestModel
    type: object
  SnsPostItemModel:
    properties:
      BlackList:
        description: ' 不可见好友列表'
        items:
          type: string
        type: array
      Content:
        description: ' 文本内容'
        example: ""
        type: string
      ContentStyle:
        description: ' 纯文字/图文/引用/视频'
        format: uint32
        type: integer
      ContentUrl:
        example: ""
        type: string
      Description:
        example: ""
        type: string
      GroupUserList:
        description: ' 可见好友列表'
        items:
          type: string
        type: array
      LocationInfo:
        $ref: '#/definitions/SnsLocationInfoModel'
        description: ' 发送朋友圈的位置信息'
      MediaList:
        description: ' 图片/视频列表'
        items:
          $ref: '#/definitions/SnsMediaItemModel'
        type: array
      Privacy:
        description: ' 是否仅自己可见'
        format: uint32
        type: integer
      WithUserList:
        description: ' 提醒好友看列表'
        items:
          type: string
        type: array
    title: SnsPostItemModel
    type: object
  SnsVideoItemModel:
    properties:
      ThumbData:
        items:
          format: int
          type: integer
        type: array
      VideoData:
        items:
          format: int
          type: integer
        type: array
    title: SnsVideoItemModel
    type: object
  StreamVideo:
    properties:
      StreamVideoThumbURL:
        example: ""
        type: string
      StreamVideoURL:
        example: ""
        type: string
      StreamVideoWebURL:
        example: ""
        type: string
    title: StreamVideo
    type: object
  SyncMessageModel:
    properties:
      Count:
        description: ' 同步几条消息; 接收空请求体, 默认为0, 同步所有消息'
        format: int
        type: integer
    title: SyncMessageModel
    type: object
  Thumb:
    properties:
      EncIdx:
        example: ""
        type: string
      Key:
        example: ""
        type: string
      Token:
        example: ""
        type: string
      Type:
        example: ""
        type: string
      Value:
        example: ""
        type: string
    title: Thumb
    type: object
  TimelineObject:
    properties:
      ActionInfo:
        $ref: '#/definitions/ActionInfo'
      AppInfo:
        $ref: '#/definitions/AppInfo'
      ContentDesc:
        example: ""
        type: string
      ContentDescScene:
        format: uint32
        type: integer
      ContentDescShowType:
        format: uint32
        type: integer
      ContentObject:
        $ref: '#/definitions/ContentObject'
      CreateTime:
        format: uint32
        type: integer
      ID:
        format: uint64
        type: integer
      Location:
        $ref: '#/definitions/Location'
      Private:
        format: uint32
        type: integer
      PublicUserName:
        example: ""
        type: string
      ShowFlag:
        format: uint32
        type: integer
      SightFolded:
        format: uint32
        type: integer
      SourceNickName:
        example: ""
        type: string
      SourceUserName:
        example: ""
        type: string
      StatExtStr:
        example: ""
        type: string
      StatisticsData:
        example: ""
        type: string
      StreamVideo:
        $ref: '#/definitions/StreamVideo'
      UserName:
        example: ""
        type: string
    title: TimelineObject
    type: object
  TransferGroupOwnerModel:
    properties:
      ChatRoomName:
        description: ' 群聊ID：xxx@chatroom'
        example: ""
        type: string
      NewOwnerUserName:
        example: ""
        type: string
    title: TransferGroupOwnerModel
    type: object
  TransmitFriendCircleModel:
    properties:
      SourceID:
        example: ""
        type: string
    title: TransmitFriendCircleModel
    type: object
  URL:
    properties:
      EncIdx:
        example: ""
        type: string
      Key:
        example: ""
        type: string
      MD5:
        example: ""
        type: string
      Token:
        example: ""
        type: string
      Type:
        example: ""
        type: string
      Value:
        example: ""
        type: string
      VideoMD5:
        example: ""
        type: string
    title: URL
    type: object
  UpdateAutopassModel:
    properties:
      SwitchType:
        format: uint32
        type: integer
    title: UpdateAutopassModel
    type: object
  UpdateChatroomAnnouncementModel:
    properties:
      ChatRoomName:
        description: ' 群聊ID：xxx@chatroom'
        example: ""
        type: string
      Content:
        example: ""
        type: string
    title: UpdateChatroomAnnouncementModel
    type: object
  UpdateNickNameModel:
    properties:
      Scene:
        format: uint32
        type: integer
      Val:
        example: ""
        type: string
    title: UpdateNickNameModel
    type: object
  UpdateSexModel:
    properties:
      City:
        example: ""
        type: string
      Country:
        example: ""
        type: string
      Province:
        example: ""
        type: string
      Sex:
        format: uint32
        type: integer
    title: UpdateSexModel
    type: object
  UpdateStepNumberModel:
    properties:
      Number:
        format: uint64
        type: integer
    title: UpdateStepNumberModel
    type: object
  UploadFriendCircleModel:
    properties:
      ImageDataList:
        items:
          type: string
        type: array
      VideoDataList:
        items:
          type: string
        type: array
    title: UploadFriendCircleModel
    type: object
  UploadHeadImageModel:
    properties:
      Base64:
        example: ""
        type: string
    title: UploadHeadImageModel
    type: object
  UploadMContactModel:
    properties:
      Mobile:
        example: ""
        type: string
      MobileList:
        items:
          type: string
        type: array
    title: UploadMContactModel
    type: object
  UserRankLikeModel:
    properties:
      RankId:
        example: ""
        type: string
    title: UserRankLikeModel
    type: object
  VerifyUserItem:
    properties:
      Gh:
        example: ""
        type: string
      Scene:
        format: int
        type: integer
    title: VerifyUserItem
    type: object
  VerifyUserRequestModel:
    properties:
      ChatRoomUserName:
        description: ' 通过群来添加好友 需要设置此值为群id'
        example: ""
        type: string
      OpCode:
        description: ' 操作类型: 1(免验证发送请求) 2(添加好友/发送验证申请) 3(同意好友/通过好友验证) 4(拒绝好友)'
        example: "2"
        format: uint32
        type: integer
      Scene:
        description: ' 添加来源, 同意添加好友时传回调消息xml中的scene值.<br/>添加好友时的枚举值如下: <br/>1(QQ)
          2(邮箱) 3(微信号) 4(QQ好友) 8(来自群聊) 13(通讯录)<br/>14(群聊) 15(手机号) 18(附近的人) 25(漂流瓶)
          29(摇一摇) 30(二维码)'
        example: "3"
        format: int
        type: integer
      V3:
        description: ' V3用户名数据(SearchContact请求返回的UserValue)'
        example: ""
        type: string
      V4:
        description: ' V4校验数据(SearchContact请求返回的AntispamTicket)'
        example: ""
        type: string
      VerifyContent:
        description: ' 添加好友时的(招呼语/验证信息)'
        example: ""
        type: string
    title: VerifyUserRequestModel
    type: object
  VideoSize:
    properties:
      Height:
        example: ""
        type: string
      Width:
        example: ""
        type: string
    title: VideoSize
    type: object
  WxBindOpMobileForModel:
    properties:
      OpCode:
        format: int64
        type: integer
      PhoneNumber:
        example: ""
        type: string
      Proxy:
        example: ""
        type: string
      Reg:
        format: uint64
        type: integer
      VerifyCode:
        example: ""
        type: string
    title: WxBindOpMobileForModel
    type: object
  WxFunctionSwitchModel:
    properties:
      Function:
        format: uint32
        type: integer
      Value:
        format: uint32
        type: integer
    title: WxFunctionSwitchModel
    type: object
  baseinfo.AlgorithmInfo:
    properties:
      BlockSize:
        description: ' 块大小'
        format: uint32
        type: integer
      Certificate:
        description: ' 证书'
        items:
          format: int
          type: integer
        type: array
      DeviceToken:
        $ref: '#/definitions/wechat.TrustResp'
        description: ' 设备令牌'
      EncryptionType:
        description: ' 加密类型'
        example: ""
        type: string
      Hash:
        description: ' 算法哈希'
        example: ""
        type: string
      IV:
        description: ' 算法IV'
        items:
          format: int
          type: integer
        type: array
      Iterations:
        description: ' 迭代次数'
        format: uint32
        type: integer
      Key:
        description: ' 算法密钥'
        items:
          format: int
          type: integer
        type: array
      KeyLength:
        description: ' 密钥长度'
        format: uint32
        type: integer
      Mode:
        description: ' 加密模式'
        example: ""
        type: string
      Padding:
        description: ' 填充方式'
        example: ""
        type: string
      PrivateKey:
        description: ' 私钥'
        items:
          format: int
          type: integer
        type: array
      PublicKey:
        description: ' 公钥'
        items:
          format: int
          type: integer
        type: array
      Salt:
        description: ' 算法盐值'
        items:
          format: int
          type: integer
        type: array
      SignatureType:
        description: ' 签名类型'
        example: ""
        type: string
      TrustChain:
        description: ' 信任链'
        items:
          format: int
          type: integer
        type: array
      Type:
        description: ' 算法类型'
        example: ""
        type: string
      Version:
        description: ' 算法版本'
        example: ""
        type: string
    title: baseinfo.AlgorithmInfo
    type: object
  baseinfo.DeviceInfo:
    properties:
      AdSource:
        description: ' 广告来源'
        example: ""
        type: string
      BaseLibVersion:
        description: ' 基础库版本'
        example: ""
        type: string
      BundleID:
        description: ' 包ID'
        example: ""
        type: string
      CarrierName:
        description: ' 运营商名称'
        example: ""
        type: string
      ClientCheckDataXML:
        description: ' 客户端检查数据XML'
        example: ""
        type: string
      ClientVersion:
        description: ' 客户端版本'
        example: ""
        type: string
      CoreCount:
        description: ' CPU核心数'
        format: uint32
        type: integer
      DPI:
        description: ' 屏幕DPI'
        format: uint32
        type: integer
      DeviceBrand:
        description: ' 设备品牌'
        example: ""
        type: string
      DeviceID:
        description: ' 设备ID'
        items:
          format: int
          type: integer
        type: array
      DeviceModel:
        description: ' 设备型号'
        example: ""
        type: string
      DeviceName:
        description: ' 设备名称'
        example: ""
        type: string
      DeviceType:
        description: ' 设备类型'
        example: ""
        type: string
      GUID2:
        description: ' GUID2'
        example: ""
        type: string
      H5Version:
        description: ' H5版本'
        example: ""
        type: string
      Imei:
        description: ' IMEI'
        example: ""
        type: string
      IphoneVer:
        description: ' iOS版本'
        example: ""
        type: string
      Language:
        description: ' 语言'
        example: ""
        type: string
      LiteAppVersion:
        description: ' 轻应用版本'
        example: ""
        type: string
      MacAddress:
        description: ' MAC地址'
        example: ""
        type: string
      NetworkType:
        description: ' 网络类型'
        example: ""
        type: string
      OsType:
        description: ' 系统类型'
        example: ""
        type: string
      OsTypeNumber:
        description: ' 系统版本号'
        example: ""
        type: string
      RealCountry:
        description: ' 国家'
        example: ""
        type: string
      ScreenHeight:
        description: ' 屏幕高度'
        format: uint32
        type: integer
      ScreenWidth:
        description: ' 屏幕宽度'
        format: uint32
        type: integer
      SoftTypeXML:
        description: ' 软件类型XML'
        example: ""
        type: string
      TimeZone:
        description: ' 时区'
        example: ""
        type: string
      UUIDOne:
        description: ' UUID1'
        example: ""
        type: string
      UUIDTwo:
        description: ' UUID2'
        example: ""
        type: string
      WifiBSSID:
        description: ' WiFi BSSID'
        example: ""
        type: string
      WifiSSID:
        description: ' WiFi SSID'
        example: ""
        type: string
    title: baseinfo.DeviceInfo
    type: object
  baseinfo.Location:
    properties:
      City:
        example: ""
        type: string
      Latitude:
        example: ""
        type: string
      Longitude:
        example: ""
        type: string
      PoiAddress:
        example: ""
        type: string
      PoiClassifyID:
        example: ""
        type: string
      PoiClassifyType:
        format: uint32
        type: integer
      PoiName:
        example: ""
        type: string
    title: baseinfo.Location
    type: object
  baseinfo.SecurityInfo:
    properties:
      AlgorithmInfo:
        $ref: '#/definitions/AlgorithmInfo'
        description: ' 算法信息'
      DecryptedData:
        description: ' 解密数据'
        items:
          format: int
          type: integer
        type: array
      DeviceInfo:
        $ref: '#/definitions/DeviceInfo'
        description: ' 设备信息'
      EncryptedData:
        description: ' 加密数据'
        items:
          format: int
          type: integer
        type: array
      LastUpdateTime:
        description: ' 最后更新时间'
        format: uint64
        type: integer
      SecurityLevel:
        description: ' 安全等级'
        format: uint32
        type: integer
      SessionExpireTime:
        description: ' 会话过期时间'
        format: uint64
        type: integer
      SessionID:
        description: ' 会话ID'
        example: ""
        type: string
      SessionKey:
        description: ' 会话密钥'
        items:
          format: int
          type: integer
        type: array
      Signature:
        description: ' 签名'
        items:
          format: int
          type: integer
        type: array
      TrustStatus:
        description: ' 信任状态'
        format: uint32
        type: integer
      VerifyData:
        description: ' 验证数据'
        items:
          format: int
          type: integer
        type: array
    title: baseinfo.SecurityInfo
    type: object
  baseinfo.UserLabelInfoItem:
    properties:
      LabelIDList:
        example: ""
        type: string
      UserName:
        example: ""
        type: string
    title: baseinfo.UserLabelInfoItem
    type: object
  wechat.TrustResp:
    properties:
      BaseResponse:
        $ref: '#/definitions/BaseResponse'
        description: ' 基础响应'
      TrustResponseData:
        $ref: '#/definitions/TrustResponseData'
        description: ' 信任响应数据'
    title: wechat.TrustResp
    type: object
  wechat.TrustResponseData:
    properties:
      DeviceToken:
        description: ' 设备令牌'
        example: ""
        type: string
      SoftData:
        $ref: '#/definitions/TrustSoftData'
        description: ' 软件数据'
      Timestamp:
        description: ' 时间戳'
        format: uint64
        type: integer
    title: wechat.TrustResponseData
    type: object
  wechat.TrustSoftData:
    properties:
      SoftConfig:
        description: ' 软件配置'
        example: ""
        type: string
      SoftData:
        description: ' 软件数据'
        items:
          format: int
          type: integer
        type: array
    title: wechat.TrustSoftData
    type: object
info:
  contact: ""
  description: |-
    WeChatPadPro-8059 iOS18.3.2 mcp服务器版 mcp地址 http://wxmcp.knowhub.cloud/ (完美兼容mcp服务MCP模型上下文协议，提供LLM与外部系统的标准化交互接口支持长链接、自动心跳、自动二次登录、重启API服务自动登录，开源生态：兼容HuggingFace/OpenAI等主流模型平台)

    服务已启动(SSE模式)，访问地址: http://localhost:8099
  title: WeChatPadPro-8059 iOS18.3.2 最新修订mcp服务器版（SSE模式）
  version: 仅供学习交流使用，禁止用于非法用途
paths:
  /admin/DelayAuthKey:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/DelayAuthKeyModel'
      responses:
        "200":
          description: ""
      summary: 授权码延期
      tags:
      - 管理
  /admin/DeleteAuthKey:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/DeleteAuthKeyModel'
      responses:
        "200":
          description: ""
      summary: 删除授权码
      tags:
      - 管理
  /admin/GenAuthKey1:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GenAuthKeyModel'
      responses:
        "200":
          description: ""
      summary: 生成授权码(新设备)
      tags:
      - 管理
  /admin/GenAuthKey2:
    get:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: ""
      summary: 生成授权码(新设备)
      tags:
      - 管理
  /applet/AuthMpLogin:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GetMpA8KeyModel'
      responses:
        "200":
          description: ""
      summary: 授权公众号登录
      tags:
      - 公众号/小程序
  /applet/FollowGH:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/FollowGHModel'
      responses:
        "200":
          description: ""
      summary: 关注公众号
      tags:
      - 公众号/小程序
  /applet/GetA8Key:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GetA8KeyRequestModel'
      responses:
        "200":
          description: ""
      summary: 授权链接
      tags:
      - 公众号/小程序
  /applet/GetMpA8Key:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GetMpA8KeyModel'
      responses:
        "200":
          description: ""
      summary: 授权链接
      tags:
      - 公众号/小程序
  /applet/GetMpHistoryMessage:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GetMpHistoryMsgModel'
      responses:
        "200":
          description: ""
      summary: 获取公众号历史消息
      tags:
      - 公众号/小程序
  /applet/JSOperateWxData:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/AppletModel'
      responses:
        "200":
          description: ""
      summary: 小程序云函数操作
      tags:
      - 公众号/小程序
  /applet/JsLogin:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/AppletModel'
      responses:
        "200":
          description: ""
      summary: 授权小程序(返回授权后的code)
      tags:
      - 公众号/小程序
  /applet/QRConnectAuthorize:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/QRConnectAuthorizeModel'
      responses:
        "200":
          description: ""
      summary: 二维码授权请求
      tags:
      - 公众号/小程序
  /applet/QRConnectAuthorizeConfirm:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/QRConnectAuthorizeModel'
      responses:
        "200":
          description: ""
      summary: 二维码授权确认
      tags:
      - 公众号/小程序
  /applet/SdkOauthAuthorize:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/AppletModel'
      responses:
        "200":
          description: ""
      summary: 应用授权
      tags:
      - 公众号/小程序
  /equipment/DelSafeDevice:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/DelSafeDeviceModel'
      responses:
        "200":
          description: ""
      summary: 删除安全设备
      tags:
      - 设备
  /equipment/GetBoundHardDevice:
    get:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: ""
      summary: 获取硬件设备情况
      tags:
      - 设备
  /equipment/GetOnlineInfo:
    get:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: ""
      summary: 获取在线设备信息
      tags:
      - 设备
  /equipment/GetSafetyInfo:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: ""
      summary: 获取安全设备列表
      tags:
      - 设备
  /favor/BatchDelFavItem:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/FavInfoModel'
      responses:
        "200":
          description: ""
      summary: 删除收藏
      tags:
      - 收藏
  /favor/FavSync:
    get:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: ""
      summary: 同步收藏
      tags:
      - 收藏
  /favor/GetFavItemId:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/FavInfoModel'
      responses:
        "200":
          description: ""
      summary: 获取收藏详细
      tags:
      - 收藏
  /favor/GetFavList:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/FavInfoModel'
      responses:
        "200":
          description: ""
      summary: 获取收藏list
      tags:
      - 收藏
  /finder/FinderFollow:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/FinderFollowModel'
      responses:
        "200":
          description: ""
      summary: 关注取消
      tags:
      - 视频号
  /finder/FinderSearch:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/FinderSearchModel'
      responses:
        "200":
          description: ""
      summary: 视频号搜索
      tags:
      - 视频号
  /finder/FinderUserPrepare:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/FinderUserPrepareModel'
      responses:
        "200":
          description: ""
      summary: 视频号中心
      tags:
      - 视频号
  /friend/AgreeAdd:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/VerifyUserRequestModel'
      responses:
        "200":
          description: ""
      summary: 同意好友请求
      tags:
      - 朋友
  /friend/DelContact:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/DelContactModel'
      responses:
        "200":
          description: ""
      summary: 删除好友
      tags:
      - 朋友
  /friend/GetContactDetailsList:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/BatchGetContactModel'
      responses:
        "200":
          description: ""
      summary: 获取联系人详情
      tags:
      - 朋友
  /friend/GetContactList:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GetContactListModel'
      responses:
        "200":
          description: ""
      summary: 获取全部联系人
      tags:
      - 朋友
  /friend/GetFriendList:
    get:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: ""
      summary: 获取好友列表
      tags:
      - 朋友
  /friend/GetFriendRelation:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GetFriendRelationModel'
      responses:
        "200":
          description: ""
      summary: 获取好友关系
      tags:
      - 朋友
  /friend/GetGHList:
    get:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: ""
      summary: 获取关注的公众号列表
      tags:
      - 朋友
  /friend/GetMFriend:
    get:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: ""
      summary: 获取手机通讯录好友
      tags:
      - 朋友
  /friend/GroupList:
    get:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: ""
      summary: 获取保存的群聊列表
      tags:
      - 朋友
  /friend/SearchContact:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/SearchContactRequestModel'
      responses:
        "200":
          description: ""
      summary: 搜索联系人
      tags:
      - 朋友
  /friend/UploadMContact:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/UploadMContactModel'
      responses:
        "200":
          description: ""
      summary: 上传手机通讯录好友
      tags:
      - 朋友
  /friend/VerifyUser:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/VerifyUserRequestModel'
      responses:
        "200":
          description: ""
      summary: 验证好友/添加好友
      tags:
      - 朋友
  /group/AddChatRoomMembers:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/InviteChatroomMembersModel'
      responses:
        "200":
          description: ""
      summary: 添加群成员
      tags:
      - 群管理
  /group/AddChatroomAdmin:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/ChatroomMemberModel'
      responses:
        "200":
          description: ""
      summary: 添加群管理员
      tags:
      - 群管理
  /group/CreateChatRoom:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/CreateChatRoomModel'
      responses:
        "200":
          description: ""
      summary: 创建群请求
      tags:
      - 群管理
  /group/DelChatroomAdmin:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/ChatroomMemberModel'
      responses:
        "200":
          description: ""
      summary: 删除群管理员
      tags:
      - 群管理
  /group/GetChatRoomInfo:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/ChatRoomWxIdListModel'
      responses:
        "200":
          description: ""
      summary: 获取群详情
      tags:
      - 群管理
  /group/GetChatroomMemberDetail:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GetChatroomMemberDetailModel'
      responses:
        "200":
          description: ""
      summary: 获取群成员详细
      tags:
      - 群管理
  /group/GetChatroomQrCode:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GetChatroomQrCodeModel'
      responses:
        "200":
          description: ""
      summary: 获取群二维码
      tags:
      - 群管理
  /group/GroupList:
    get:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: ""
      summary: 获取群列表
      tags:
      - 群管理
  /group/InviteChatroomMembers:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/InviteChatroomMembersModel'
      responses:
        "200":
          description: ""
      summary: 邀请群成员
      tags:
      - 群管理
  /group/MoveToContract:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/MoveContractModel'
      responses:
        "200":
          description: ""
      summary: 获取群聊
      tags:
      - 群管理
  /group/QuitChatroom:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GetChatroomMemberDetailModel'
      responses:
        "200":
          description: ""
      summary: 退出群聊
      tags:
      - 群管理
  /group/ScanIntoUrlGroup:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/ScanIntoUrlGroupModel'
      responses:
        "200":
          description: ""
      summary: 扫码入群
      tags:
      - 群管理
  /group/SendDelDelChatRoomMember:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/InviteChatroomMembersModel'
      responses:
        "200":
          description: ""
      summary: 删除群成员
      tags:
      - 群管理
  /group/SendPat:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/SendPatModel'
      responses:
        "200":
          description: ""
      summary: 群拍一拍功能
      tags:
      - 群管理
  /group/SendTransferGroupOwner:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/TransferGroupOwnerModel'
      responses:
        "200":
          description: ""
      summary: 转让群
      tags:
      - 群管理
  /group/SetChatroomAccessVerify:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/SetChatroomAccessVerifyModel'
      responses:
        "200":
          description: ""
      summary: 设置群聊邀请开关
      tags:
      - 群管理
  /group/SetChatroomAnnouncement:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/UpdateChatroomAnnouncementModel'
      responses:
        "200":
          description: ""
      summary: 设置群公告
      tags:
      - 群管理
  /group/SetChatroomName:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/ChatroomNameModel'
      responses:
        "200":
          description: ""
      summary: 设置群昵称
      tags:
      - 群管理
  /group/SetGetChatRoomInfoDetail:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GetChatroomMemberDetailModel'
      responses:
        "200":
          description: ""
      summary: 获取群公告
      tags:
      - 群管理
  /label/AddContactLabel:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/LabelModel'
      responses:
        "200":
          description: ""
      summary: 添加列表
      tags:
      - 标签
  /label/DelContactLabel:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/LabelModel'
      responses:
        "200":
          description: ""
      summary: 删除标签
      tags:
      - 标签
  /label/GetContactLabelList:
    get:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: ""
      summary: 获取标签列表
      tags:
      - 标签
  /label/GetWXFriendListByLabel:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/LabelModel'
      responses:
        "200":
          description: ""
      summary: 获取标签下所有好友
      tags:
      - 标签
  /label/ModifyLabel:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/LabelModel'
      responses:
        "200":
          description: ""
      summary: 修改标签
      tags:
      - 标签
  /login/A16Login:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/DeviceIdLoginModel'
      responses:
        "200":
          description: ""
      summary: 数据登录
      tags:
      - 登录
  /login/CheckCanSetAlias:
    get:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: ""
      summary: 检测微信登录环境
      tags:
      - 登录
  /login/CheckLoginStatus:
    get:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: ""
      summary: 检测扫码状态
      tags:
      - 登录
  /login/DeviceLogin:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/DeviceIdLoginModel'
      responses:
        "200":
          description: ""
      summary: 62账号密码登录
      tags:
      - 登录
  /login/Get62Data:
    get:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: ""
      summary: 提取62数据
      tags:
      - 登录
  /login/GetIWXConnect:
    get:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: ""
      summary: 打印链接数量
      tags:
      - 登录
  /login/GetInItStatus:
    get:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: ""
      summary: 初始化状态
      tags:
      - 登录
  /login/GetLoginQrCodeNew:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GetLoginQrCodeModel'
      responses:
        "200":
          description: ""
      summary: 获取登录二维码(异地IP用代理)
      tags:
      - 登录
  /login/GetLoginQrCodeNewX:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GetLoginQrCodeModel'
      responses:
        "200":
          description: ""
      summary: 获取登录二维码(绕过验证码)
      tags:
      - 登录
  /login/GetLoginStatus:
    get:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: ""
      summary: 获取在线状态
      tags:
      - 登录
  /login/LogOut:
    get:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: ""
      summary: 退出登录
      tags:
      - 登录
  /login/LoginNew:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/DeviceIdLoginModel'
      responses:
        "200":
          description: ""
      summary: 62LoginNew新疆号登录
      tags:
      - 登录
  /login/PhoneDeviceLogin:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/PhoneLoginModel'
      responses:
        "200":
          description: ""
      summary: 辅助新手机登录
      tags:
      - 登录
  /login/ShowQrCode:
    get:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: ""
      summary: HTML展示登录二维码
      tags:
      - 登录
  /login/SmsLogin:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/DeviceIdLoginModel'
      responses:
        "200":
          description: ""
      summary: 短信登录
      tags:
      - 登录
  /login/WakeUpLogin:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GetLoginQrCodeModel'
      responses:
        "200":
          description: ""
      summary: 唤醒登录(只限扫码登录)
      tags:
      - 登录
  /login/WxBindOpMobileForReg:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/WxBindOpMobileForModel'
      responses:
        "200":
          description: ""
      summary: 获取验证码
      tags:
      - 登录
  /message/AddMessageMgr:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/SendMessageModel'
      responses:
        "200":
          description: ""
      summary: 添加要发送的文本消息进入管理器
      tags:
      - 消息
  /message/CdnUploadVideo:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/CdnUploadVideoRequest'
      responses:
        "200":
          description: ""
      summary: 上传视频
      tags:
      - 消息
  /message/ForwardEmoji:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/SendEmojiMessageModel'
      responses:
        "200":
          description: ""
      summary: 转发表情，包含动图
      tags:
      - 消息
  /message/ForwardImageMessage:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/ForwardMessageModel'
      responses:
        "200":
          description: ""
      summary: 转发图片
      tags:
      - 消息
  /message/ForwardVideoMessage:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/ForwardMessageModel'
      responses:
        "200":
          description: ""
      summary: 转发视频
      tags:
      - 消息
  /message/GetMsgBigImg:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/DownloadParam'
      responses:
        "200":
          description: ""
      summary: 获取图片(高清图片下载)
      tags:
      - 消息
  /message/GetMsgVideo:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/DownloadParam'
      responses:
        "200":
          description: ""
      summary: 获取视频(视频数据下载)
      tags:
      - 消息
  /message/GetMsgVoice:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/DownloadVoiceModel'
      responses:
        "200":
          description: ""
      summary: 下载语音消息
      tags:
      - 消息
  /message/GroupMassMsgImage:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GroupMassMsgImageModel'
      responses:
        "200":
          description: ""
      summary: 群发图片
      tags:
      - 消息
  /message/GroupMassMsgText:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GroupMassMsgTextModel'
      responses:
        "200":
          description: ""
      summary: 群发接口
      tags:
      - 消息
  /message/HttpSyncMsg:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/SyncMessageModel'
      responses:
        "200":
          description: ""
      summary: 同步消息, HTTP-轮询方式
      tags:
      - 消息
  /message/NewSyncHistoryMessage:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: ""
      summary: 同步历史消息
      tags:
      - 消息
  /message/RevokeMsg:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/RevokeMsgModel'
      responses:
        "200":
          description: ""
      summary: 撤销消息
      tags:
      - 消息
  /message/RevokeMsgNew:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/RevokeMsgModel'
      responses:
        "200":
          description: ""
      summary: 撤回消息（New）
      tags:
      - 消息
  /message/SendAppMessage:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/AppMessageModel'
      responses:
        "200":
          description: ""
      summary: 发送App消息
      tags:
      - 消息
  /message/SendCdnDownload:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/DownMediaModel'
      responses:
        "200":
          description: ""
      summary: 下载 请求
      tags:
      - 消息
  /message/SendEmojiMessage:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/SendEmojiMessageModel'
      responses:
        "200":
          description: ""
      summary: 发送表情
      tags:
      - 消息
  /message/SendImageMessage:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/SendMessageModel'
      responses:
        "200":
          description: ""
      summary: 发送图片消息
      tags:
      - 消息
  /message/SendImageNewMessage:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/SendMessageModel'
      responses:
        "200":
          description: ""
      summary: 发送图片消息（New）
      tags:
      - 消息
  /message/SendTextMessage:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/SendMessageModel'
      responses:
        "200":
          description: ""
      summary: 发送文本消息
      tags:
      - 消息
  /message/SendVoice:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/SendUploadVoiceRequestModel'
      responses:
        "200":
          description: ""
      summary: 发送语音
      tags:
      - 消息
  /message/ShareCardMessage:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/ShareCardParam'
      responses:
        "200":
          description: ""
      summary: 分享名片消息
      tags:
      - 消息
  /other/GetPeopleNearby:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/PeopleNearbyModel'
      responses:
        "200":
          description: ""
      summary: 查看附近的人
      tags:
      - 其他
  /other/GetRedisSyncMsg:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: ""
      summary: 获取缓存在redis中的消息
      tags:
      - 其他
  /other/GetUserRankLikeCount:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/UserRankLikeModel'
      responses:
        "200":
          description: ""
      summary: 获取步数排行数据列表
      tags:
      - 其他
  /other/RedisMemory:
    get:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: ""
      summary: 内存管理
      tags:
      - 其他
  /other/UpdateStepNumber:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/UpdateStepNumberModel'
      responses:
        "200":
          description: ""
      summary: 修改步数
      tags:
      - 其他
  /pay/Collectmoney:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/CollectmoneyModel'
      responses:
        "200":
          description: ""
      summary: 确定收款
      tags:
      - 支付
  /pay/ConfirmPreTransfer:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/ConfirmPreTransfer'
      responses:
        "200":
          description: ""
      summary: 确认转账(客户端版本过低会无法转账)
      tags:
      - 支付
  /pay/CreatePreTransfer:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/CreatePreTransfer'
      responses:
        "200":
          description: ""
      summary: 创建转账
      tags:
      - 支付
  /pay/GeneratePayQCode:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GeneratePayQCodeModel'
      responses:
        "200":
          description: ""
      summary: 生成自定义收款二维码
      tags:
      - 支付
  /pay/GetBandCardList:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: ""
      summary: 获取银行卡信息
      tags:
      - 支付
  /pay/GetRedEnvelopesDetail:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/HongBaoItem'
      responses:
        "200":
          description: ""
      summary: 查看红包详情
      tags:
      - 支付
  /pay/GetRedPacketList:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GetRedPacketList'
      responses:
        "200":
          description: ""
      summary: 查看红包领取列表
      tags:
      - 支付
  /pay/OpenRedEnvelopes:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/HongBaoItem'
      responses:
        "200":
          description: ""
      summary: 拆红包
      tags:
      - 支付
  /pay/WXCreateRedPacket:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/RedPacket'
      responses:
        "200":
          description: ""
      summary: 创建红包
      tags:
      - 支付
  /qy/QWAcceptChatRoom:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/QWAcceptChatRoomModel'
      responses:
        "200":
          description: ""
      summary: 同意进企业群
      tags:
      - 企业微信
  /qy/QWAddChatRoomMember:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/QWAddChatRoomMemberModel'
      responses:
        "200":
          description: ""
      summary: 直接拉朋友进企业群
      tags:
      - 企业微信
  /qy/QWAdminAcceptJoinChatRoomSet:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/QWAdminAcceptJoinChatRoomSetModel'
      responses:
        "200":
          description: ""
      summary: 设定企业群管理审核进群
      tags:
      - 企业微信
  /qy/QWApplyAddContact:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/QWApplyAddContactModel'
      responses:
        "200":
          description: ""
      summary: 向企业微信打招呼
      tags:
      - 企业微信
  /qy/QWAppointChatRoomAdmin:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/QWAddChatRoomMemberModel'
      responses:
        "200":
          description: ""
      summary: 增加企业管理员
      tags:
      - 企业微信
  /qy/QWChatRoomAnnounce:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/QWModChatRoomNameModel'
      responses:
        "200":
          description: ""
      summary: 发布企业群公告
      tags:
      - 企业微信
  /qy/QWChatRoomTransferOwner:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/QWChatRoomTransferOwnerModel'
      responses:
        "200":
          description: ""
      summary: 转让企业群
      tags:
      - 企业微信
  /qy/QWContact:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/QWContactModel'
      responses:
        "200":
          description: ""
      summary: 提取企业 wx 详情
      tags:
      - 企业微信
  /qy/QWCreateChatRoom:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/QWCreateModel'
      responses:
        "200":
          description: ""
      summary: 创建企业群
      tags:
      - 企业微信
  /qy/QWDelChatRoom:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/QWModChatRoomNameModel'
      responses:
        "200":
          description: ""
      summary: 删除企业群
      tags:
      - 企业微信
  /qy/QWDelChatRoomAdmin:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/QWAddChatRoomMemberModel'
      responses:
        "200":
          description: ""
      summary: 移除群管理员
      tags:
      - 企业微信
  /qy/QWDelChatRoomMember:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/QWAddChatRoomMemberModel'
      responses:
        "200":
          description: ""
      summary: 删除企业群成员
      tags:
      - 企业微信
  /qy/QWGetChatRoomMember:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/QWAddChatRoomMemberModel'
      responses:
        "200":
          description: ""
      summary: 提取企业群全部成员
      tags:
      - 企业微信
  /qy/QWGetChatRoomQR:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/QWAddChatRoomMemberModel'
      responses:
        "200":
          description: ""
      summary: 提取企业群二维码
      tags:
      - 企业微信
  /qy/QWGetChatroomInfo:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/QWAddChatRoomMemberModel'
      responses:
        "200":
          description: ""
      summary: 提取企业群名称公告设定等信息
      tags:
      - 企业微信
  /qy/QWInviteChatRoomMember:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/QWAddChatRoomMemberModel'
      responses:
        "200":
          description: ""
      summary: 发送群邀请链接
      tags:
      - 企业微信
  /qy/QWModChatRoomMemberNick:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/QWModChatRoomNameModel'
      responses:
        "200":
          description: ""
      summary: 修改成员在群中呢称
      tags:
      - 企业微信
  /qy/QWModChatRoomName:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/QWModChatRoomNameModel'
      responses:
        "200":
          description: ""
      summary: 修改企业群名称
      tags:
      - 企业微信
  /qy/QWRemark:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/QWRemarkModel'
      responses:
        "200":
          description: ""
      summary: 备注企业 wxid
      tags:
      - 企业微信
  /qy/QWSearchContact:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/SearchContactModel'
      responses:
        "200":
          description: ""
      summary: 搜手机或企业对外名片链接提取验证
      tags:
      - 企业微信
  /qy/QWSyncChatRoom:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/QWSyncChatRoomModel'
      responses:
        "200":
          description: ""
      summary: 提取全部企业微信群-
      tags:
      - 企业微信
  /qy/QWSyncContact:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: ""
      summary: 提取全部的企业通讯录
      tags:
      - 企业微信
  /sns/DownloadMedia:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/DownloadMediaModel'
      responses:
        "200":
          description: ""
      summary: 下载朋友圈视频
      tags:
      - 朋友圈
  /sns/GetCollectCircle:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/SendFavItemCircle'
      responses:
        "200":
          description: ""
      summary: 获取收藏朋友圈详情
      tags:
      - 朋友圈
  /sns/GetSnsSync:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: ""
      summary: 同步朋友圈
      tags:
      - 朋友圈
  /sns/SendCdnSnsVideoUploadReuqest:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: ""
      summary: 上传朋友圈视频
      tags:
      - 朋友圈
  /sns/SendFavItemCircle:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/SendFavItemCircle'
      responses:
        "200":
          description: ""
      summary: 转发收藏朋友圈
      tags:
      - 朋友圈
  /sns/SendFriendCircle:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/SnsPostItemModel'
      responses:
        "200":
          description: ""
      summary: 发送朋友圈
      tags:
      - 朋友圈
  /sns/SendFriendCircleByXMl:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/TimelineObject'
      responses:
        "200":
          description: ""
      summary: 发送朋友圈XML结构
      tags:
      - 朋友圈
  /sns/SendOneIdCircle:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GetIdDetailModel'
      responses:
        "200":
          description: ""
      summary: 一键转发朋友圈
      tags:
      - 朋友圈
  /sns/SendSnsComment:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/SendSnsCommentRequestModel'
      responses:
        "200":
          description: ""
      summary: 点赞评论
      tags:
      - 朋友圈
  /sns/SendSnsObjectDetailById:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GetIdDetailModel'
      responses:
        "200":
          description: ""
      summary: 获取指定id朋友圈
      tags:
      - 朋友圈
  /sns/SendSnsObjectOp:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/SendSnsObjectOpRequestModel'
      responses:
        "200":
          description: ""
      summary: 朋友圈操作
      tags:
      - 朋友圈
  /sns/SendSnsTimeLine:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GetSnsInfoModel'
      responses:
        "200":
          description: ""
      summary: 获取朋友圈主页
      tags:
      - 朋友圈
  /sns/SendSnsUserPage:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GetSnsInfoModel'
      responses:
        "200":
          description: ""
      summary: 获取指定人朋友圈
      tags:
      - 朋友圈
  /sns/SetBackgroundImage:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/SetBackgroundImageModel'
      responses:
        "200":
          description: ""
      summary: 设置朋友圈背景图片
      tags:
      - 朋友圈
  /sns/SetFriendCircleDays:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/SetFriendCircleDaysModel'
      responses:
        "200":
          description: ""
      summary: 设置朋友圈可见天数
      tags:
      - 朋友圈
  /sns/UploadFriendCircleImage:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/UploadFriendCircleModel'
      responses:
        "200":
          description: ""
      summary: 上传图片信息
      tags:
      - 朋友圈
  /user/ChangePwd:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/SendChangePwdRequestModel'
      responses:
        "200":
          description: ""
      summary: 更改密码
      tags:
      - 用户
  /user/GetMyQrCode:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GetQrCodeModel'
      responses:
        "200":
          description: ""
      summary: 获取我的二维码
      tags:
      - 用户
  /user/GetProfile:
    get:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: ""
      summary: 获取个人资料信息
      tags:
      - 用户
  /user/ModifyRemark:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/SendModifyRemarkRequestModel'
      responses:
        "200":
          description: ""
      summary: 修改备注
      tags:
      - 用户
  /user/ModifyUserInfo:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/ModifyUserInfo'
      responses:
        "200":
          description: ""
      summary: 修改资料
      tags:
      - 用户
  /user/SetFunctionSwitch:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/WxFunctionSwitchModel'
      responses:
        "200":
          description: ""
      summary: 设置添加我的方式
      tags:
      - 用户
  /user/SetNickName:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/UpdateNickNameModel'
      responses:
        "200":
          description: ""
      summary: 设置昵称
      tags:
      - 用户
  /user/SetProxy:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/GetLoginQrCodeModel'
      responses:
        "200":
          description: ""
      summary: 修改Socks5代理
      tags:
      - 用户
  /user/SetSendPat:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/SetSendPatModel'
      responses:
        "200":
          description: ""
      summary: 设置拍一拍名称
      tags:
      - 用户
  /user/SetSexDq:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/UpdateSexModel'
      responses:
        "200":
          description: ""
      summary: 修改性别
      tags:
      - 用户
  /user/SetSignature:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/UpdateNickNameModel'
      responses:
        "200":
          description: ""
      summary: 修改签名
      tags:
      - 用户
  /user/SetWechat:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/AlisaModel'
      responses:
        "200":
          description: ""
      summary: 设置微信号
      tags:
      - 用户
  /user/UpdateAutoPass:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/UpdateAutopassModel'
      responses:
        "200":
          description: ""
      summary: 修改加好友需要验证属性
      tags:
      - 用户
  /user/UpdateNickName:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/UpdateNickNameModel'
      responses:
        "200":
          description: ""
      summary: 修改名称
      tags:
      - 用户
  /user/UploadHeadImage:
    post:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      - description: 请求参数
        in: body
        name: body
        schema:
          $ref: '#/definitions/UploadHeadImageModel'
      responses:
        "200":
          description: ""
      summary: 上传头像
      tags:
      - 用户
  /ws/GetSyncMsg:
    get:
      parameters:
      - description: 账号唯一标识
        in: query
        name: key
        type: string
      responses:
        "200":
          description: ""
      summary: 同步消息，ws协议; 下面有【同步消息-HTTP-轮询方式】
      tags:
      - 同步消息
swagger: "2.0"
tags:
- description: /admin
  name: 管理
- description: /login
  name: 登录
- description: /ws
  name: 同步消息
- description: /message
  name: 消息
- description: /pay
  name: 支付
- description: /friend
  name: 朋友
- description: /user
  name: 用户
- description: /group
  name: 群管理
- description: /label
  name: 标签
- description: /applet
  name: 公众号/小程序
- description: /sns
  name: 朋友圈
- description: /finder
  name: 视频号
- description: /favor
  name: 收藏
- description: /qy
  name: 企业微信
- description: /equipment
  name: 设备
- description: /other
  name: 其他
