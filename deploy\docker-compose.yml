version: '3.8'

services:
  wechatpadpro:
    image: registry.cn-hangzhou.aliyuncs.com/wechatpad/wechatpadpro:v0.11
    container_name: wechatpadpro
    restart: always
    ports:
      - "1239:1239"
    volumes:
      - wechatpadpro_data:/app/data
      - wechatpadpro_logs:/app/logs
    environment:
      - TZ=Asia/Shanghai
      - DEBUG=false
      - ADMIN_KEY=12345
      - HOST=0.0.0.0
      - PORT=1239
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=1
      - REDIS_PASS=123456
      - MYSQL_CONNECT_STR=wechatpadpro:123456@tcp(mysql:3306)/wechatpadpro?charset=utf8mb4&parseTime=true&loc=Local
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - wechatpadpro_net

  redis:
    image: registry.cn-hangzhou.aliyuncs.com/wechatpad/wechatpadpro-redis:v0.1
    container_name: wechatpadpro_redis
    restart: always
    command: redis-server --requirepass 123456
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 3
    networks:
      - wechatpadpro_net

  mysql:
    image: registry.cn-hangzhou.aliyuncs.com/wechatpad/wechatpadpro-mysql:v0.11
    container_name: wechatpadpro_mysql
    restart: always
    environment:
      - MYSQL_ROOT_PASSWORD=123456
      - MYSQL_DATABASE=wechatpadpro
      - MYSQL_USER=wechatpadpro
      - MYSQL_PASSWORD=123456
      - TZ=Asia/Shanghai
    volumes:
      - mysql_data:/var/lib/mysql
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "wechatpadpro", "-p123456"]
      interval: 5s
      timeout: 3s
      retries: 3
    networks:
      - wechatpadpro_net

networks:
  wechatpadpro_net:
    driver: bridge

volumes:
  wechatpadpro_data:
  wechatpadpro_logs:
  redis_data:
  mysql_data: 
