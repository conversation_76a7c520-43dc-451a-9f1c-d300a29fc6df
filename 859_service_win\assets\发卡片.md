# 发送卡片消息请求格式

## 请求信息

### 请求URL
```
http://localhost:8059/message/SendAppMessage?key=dfa4d58e-6c12-45e6-84bb-df46f7b73451
```

### 请求方法
POST

### 请求头
```json
{
  "accept": "application/json",
  "Content-Type": "application/json"
}
```

### 请求体
```json
{
  "AppList": [
    {
      "ContentType": 5,
      "ContentXML": "<appmsg appid=\"\" sdkver=\"0\"><title>12555555消息推送服务-消息详情</title><des>测试点击查看详情</des><action>view</action><type>5</type><showtype>0</showtype><soundtype>0</soundtype><mediatagname></mediatagname><messageext></messageext><messageaction></messageaction><content></content><contentattr>0</contentattr><url>https://wxpusher.zjiecode.com/api/message/1K6ZVo6n5wMPOIFAl7amiBKPTXsEUaHN</url><lowurl></lowurl><dataurl></dataurl><lowdataurl></lowdataurl><songalbumurl></songalbumurl><songlyric></songlyric><appattach><totallen>0</totallen><attachid></attachid><fileext></fileext><cdnthumbaeskey></cdnthumbaeskey><aeskey></aeskey></appattach><extinfo></extinfo><sourceusername></sourceusername><sourcedisplayname></sourcedisplayname><thumburl></thumburl><md5></md5><statextstr></statextstr><directshare>0</directshare></appmsg>",
      "ToUserName": "45661415115@chatroom"
    }
  ]
}
```

### 响应示例
```json
{
  "Code": 200,
  "Data": [
    {
      "contentType": 5,
      "contentXML": "<appmsg appid=\"\" sdkver=\"0\"><title>12555555消息推送服务-消息详情</title><des>测试点击查看详情</des><action>view</action><type>5</type><showtype>0</showtype><soundtype>0</soundtype><mediatagname></mediatagname><messageext></messageext><messageaction></messageaction><content></content><contentattr>0</contentattr><url>https://wxpusher.zjiecode.com/api/message/1K6ZVo6n5wMPOIFAl7amiBKPTXsEUaHN</url><lowurl></lowurl><dataurl></dataurl><lowdataurl></lowdataurl><songalbumurl></songalbumurl><songlyric></songlyric><appattach><totallen>0</totallen><attachid></attachid><fileext></fileext><cdnthumbaeskey></cdnthumbaeskey><aeskey></aeskey></appattach><extinfo></extinfo><sourceusername></sourceusername><sourcedisplayname></sourcedisplayname><thumburl></thumburl><md5></md5><statextstr></statextstr><directshare>0</directshare></appmsg>",
      "isSendSuccess": true,
      "resp": {
        "BaseResponse": {
          "ret": 0,
          "errMsg": {}
        },
        "FromUserName": "wxid_j0n0oq7vxx8k12",
        "ToUserName": "45661415115@chatroom",
        "MsgId": 637135233,
        "ClientMsgId": "45661415115@chatroom_1745918909",
        "CreateTime": 1745918910,
        "Type": 5,
        "NewMsgId": 4524094063591787000
      },
      "retCode": 0,
      "toUserName": "45661415115@chatroom"
    }
  ],
  "Text": ""
}
```

## 参数说明

### 主要参数
- `ContentType`: 5 (表示链接消息)
- `ToUserName`: 接收者的微信ID或群聊ID (以@chatroom结尾表示群聊)

### XML字段说明
- `title`: 消息标题
- `des`: 消息描述
- `action`: 动作类型 (view)
- `type`: 消息类型 (5)
- `url`: 链接地址
- `showtype`: 显示类型
- `soundtype`: 声音类型
- `contentattr`: 内容属性
- `directshare`: 直接分享标志

### 响应字段说明
- `Code`: 200 表示请求成功
- `isSendSuccess`: true 表示消息发送成功
- `resp.BaseResponse.ret`: 0 表示微信服务器处理成功
- `MsgId`: 消息ID
- `ClientMsgId`: 客户端消息ID
- `CreateTime`: 消息创建时间戳
- `Type`: 消息类型
- `NewMsgId`: 新消息ID

## 注意事项
1. 确保服务器地址正确 (localhost:8059)
2. API key 必须有效
3. 接收者ID必须正确（个人ID或群聊ID）
4. 链接地址必须有效
5. 所有必填字段不能为空
6. 群聊ID必须以@chatroom结尾 