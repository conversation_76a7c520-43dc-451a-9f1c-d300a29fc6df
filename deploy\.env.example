# ===========================================
# 基础配置说明
# ===========================================

# 调试模式 (true: 开启调试模式, false: 生产模式)
# 默认值: true
DEBUG=true

# ===========================================
# 服务配置
# ===========================================

# 服务监听地址 (0.0.0.0表示监听所有网卡)
# 默认值: 0.0.0.0
HOST=0.0.0.0

# 服务端口号 (注意不要与其他服务冲突)
# 默认值: 8059
PORT=8080

# API版本前缀 (例如: /v1, /v2)
# 默认值: 空
API_VERSION=

# 引流公众号微信ID (用于新用户首次登录时推广)
# 默认值: 空
GH_WXID=

# 管理接口授权密钥 (重要: 请修改为安全的随机字符串)
# 默认值: stay33
ADMIN_KEY=stay33

# ===========================================
# 工作池配置
# ===========================================

# 工作池大小 (并发处理任务的goroutine数量)
# 默认值: 500
WORKER_POOL_SIZE=500

# 工作池最大任务队列长度
# 默认值: 1000
MAX_WORKER_TASK_LEN=1000

# ===========================================
# Redis配置
# ===========================================

# Redis服务器地址
# 默认值: wechatpadpro_redis
REDIS_HOST=wechatpadpro_redis

# Redis端口
# 默认值: 6379
REDIS_PORT=6379

# Redis数据库编号
# 默认值: 1
REDIS_DB=1

# Redis用户名 (如果有)
# 默认值: 空
REDIS_USER=

# Redis密码
# 默认值: 123456
REDIS_PASS=123456

# Redis连接池配置
# 最大空闲连接数
# 默认值: 10
REDIS_MAX_IDLE=10

# 最大活动连接数
# 默认值: 20
REDIS_MAX_ACTIVE=20

# 空闲连接超时时间(毫秒)
# 默认值: 5000
REDIS_IDLE_TIMEOUT=5000

# 连接最大生命周期(秒)
# 默认值: 30
REDIS_MAX_CONN_LIFETIME=30

# 连接超时时间(秒)
# 默认值: 0 (不超时)
REDIS_CONNECT_TIMEOUT=0

# ===========================================
# MySQL配置
# ===========================================

# MySQL连接字符串
# 格式: 用户名:密码@tcp(主机:端口)/数据库名?参数
# 默认值: weixin:123456@tcp(wechatpadpro_mysql:3306)/weixin?charset=utf8mb4&parseTime=true&loc=Local
MYSQL_CONNECT_STR=weixin:123456@tcp(wechatpadpro_mysql:3306)/weixin?charset=utf8mb4&parseTime=true&loc=Local

# ===========================================
# Web服务配置
# ===========================================

# Web域名
# 默认值: 空
WEB_DOMAIN=

# Web任务名称
# 默认值: 空
WEB_TASK_NAME=

# Web任务应用编号
# 默认值: 空
WEB_TASK_APP_NUMBER=

# ===========================================
# 消息同步配置
# ===========================================

# 是否按微信ID同步消息
# 默认值: true
NEWS_SYN_WXID=true

# 禁用的命令列表 (多个命令用逗号分隔)
# 默认值: A000
DISABLED_CMD_LIST=A000

# ===========================================
# 任务配置
# ===========================================

# 任务执行等待时间(毫秒)
# 默认值: 500
TASK_EXEC_WAIT_TIMES=500

# 队列过期时间(秒)
# 默认值: 86400 (24小时)
QUEUE_EXPIRE_TIME=86400

# 任务重试次数
# 默认值: 3
TASK_RETRY_COUNT=3

# 任务重试间隔(秒)
# 默认值: 5
TASK_RETRY_INTERVAL=5

# 心跳包间隔(秒)
# 默认值: 60
HEARTBEAT_INTERVAL=60

# 自动认证间隔(分钟)
# 默认值: 30
AUTO_AUTH_INTERVAL=30

# ===========================================
# 消息队列配置
# ===========================================

# RocketMQ配置
# 是否启用RocketMQ
# 默认值: false
ROCKET_MQ_ENABLED=false

# RocketMQ服务器地址
# 默认值: 127.0.0.1:9876
ROCKET_MQ_HOST=127.0.0.1:9876

# RocketMQ访问密钥
# 默认值: 123
ROCKET_ACCESS_KEY=123

# RocketMQ密钥
# 默认值: 123!#@13$
ROCKET_SECRET_KEY=123!#@13$

# RabbitMQ配置
# 是否启用RabbitMQ
# 默认值: false
RABBIT_MQ_ENABLED=false

# RabbitMQ连接URL
# 格式: amqp://用户名:密码@主机:端口/
# 默认值: amqp://yunkong:123456@127.0.0.1:5672/
RABBIT_MQ_URL=amqp://yunkong:123456@127.0.0.1:5672/

# 消息主题
# 默认值: wx_sync_msg_topic
TOPIC=wx_sync_msg_topic

# Kafka配置
# 是否启用Kafka
# 默认值: false
KAFKA_ENABLED=false

# Kafka服务器地址列表
# 默认值: 空
KAFKA_URL=***REMOVED***:9093,***REMOVED***:9093,***REMOVED***:9093

# Kafka用户名
# 默认值: 空
KAFKA_USERNAME=yunkongkafka

# Kafka密码
# 默认值: 空
KAFKA_PASSWORD=iSPmfJmtBgcvcTJZ123456

# ===========================================
# 其他配置
# ===========================================

# 是否启用DT
# 默认值: true
DT=true

# ===========================================
# 集群配置
# ===========================================

# 集群名称
# 默认值: 空
CLUSTER_NAME=

# ZooKeeper地址
# 默认值: 空
ZK_ADDR=

# ETCD地址
# 默认值: 空
ETCD_ADDR=

# ===========================================
# 容器配置
# ===========================================

# MySQL root密码
# 重要: 生产环境请修改为安全的密码
MYSQL_ROOT_PASSWORD=change_this_root_password

# MySQL数据库名
MYSQL_DATABASE=wechatpadpro

# MySQL用户名
MYSQL_USER=wechatpadpro

# MySQL密码
# 重要: 生产环境请修改为安全的密码
MYSQL_PASSWORD=change_this_password

# Redis密码
# 重要: 生产环境请修改为安全的密码
REDIS_PASSWORD=change_this_redis_password

# 应用端口
APP_PORT=8080

# 时区设置
TZ=Asia/Shanghai

# ===========================================
# 可选配置
# ===========================================

# MySQL端口 (可选)
# MYSQL_PORT=3306

# Redis端口 (可选)
# REDIS_PORT=6379

# 应用调试模式 (可选)
# APP_DEBUG=false 